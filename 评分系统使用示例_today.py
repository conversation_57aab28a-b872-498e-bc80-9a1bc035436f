#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票技术指标评分系统使用示例
演示如何使用评分函数进行股票数据分析
"""

from 股票技术指标评分系统 import score_stock_data, batch_score_files
import pandas as pd
import os

def example_single_file_scoring():
    """示例1：单文件评分"""
    print("=" * 60)
    print("示例1：单文件评分")
    print("=" * 60)
    
    # 数据文件路径
    data_file = r"c:\Zhou\coding\gupiao\fenxi\15di_fantan_2019110120250622.csv"
    
    # 执行评分
    result_df = score_stock_data(data_file)
    
    if result_df is not None:
        print(f"\n✓ 评分完成！")
        
        # 显示高分股票
        high_score_stocks = result_df[result_df['综合评分'] >= 90]
        print(f"\n高分股票（90分以上）共 {len(high_score_stocks)} 只：")
        
        if len(high_score_stocks) > 0:
            display_columns = ['secID', 'secShortName', '综合评分', '评分详情']
            available_columns = [col for col in display_columns if col in high_score_stocks.columns]
            print(high_score_stocks[available_columns].head(10).to_string(index=False))
        
        # 分析评分分布
        print(f"\n评分分布分析：")
        score_ranges = [
            (90, 100, "优秀"),
            (80, 89, "良好"), 
            (70, 79, "中等"),
            (50, 69, "及格"),
            (0, 0, "不合格")
        ]
        
        for min_score, max_score, level in score_ranges:
            if min_score == max_score:
                count = len(result_df[result_df['综合评分'] == min_score])
            else:
                count = len(result_df[(result_df['综合评分'] >= min_score) & (result_df['综合评分'] <= max_score)])
            
            percentage = (count / len(result_df)) * 100
            print(f"  {level}({min_score}-{max_score}分): {count}只 ({percentage:.2f}%)")


def example_custom_analysis():
    """示例2：自定义分析"""
    print("\n" + "=" * 60)
    print("示例2：自定义分析 - 寻找潜力股")
    print("=" * 60)
    
    # 加载评分结果
    result_files = [f for f in os.listdir('.') if f.startswith('15di_fantan') and f.endswith('评分结果_') and f.endswith('.csv')]
    
    if not result_files:
        print("未找到评分结果文件，请先运行示例1")
        return
    
    # 使用最新的评分结果文件
    latest_file = sorted(result_files)[-1]
    df = pd.read_csv(latest_file, encoding='utf-8-sig')
    
    print(f"加载评分结果文件: {latest_file}")
    print(f"总记录数: {len(df)}")
    
    # 寻找潜力股：评分在70-85分之间的股票
    potential_stocks = df[(df['综合评分'] >= 70) & (df['综合评分'] < 90)]
    
    print(f"\n潜力股分析（70-85分）：")
    print(f"潜力股数量: {len(potential_stocks)}")
    
    if len(potential_stocks) > 0:
        # 按评分排序
        potential_stocks = potential_stocks.sort_values('综合评分', ascending=False)
        
        print(f"\n前10只潜力股：")
        display_columns = ['secID', 'secShortName', '综合评分', '评分详情']
        available_columns = [col for col in display_columns if col in potential_stocks.columns]
        print(potential_stocks[available_columns].head(10).to_string(index=False))
        
        # 分析潜力股的指标特征
        print(f"\n潜力股指标特征分析：")
        
        # 统计各指标满足情况
        core_satisfied = potential_stocks['评分详情'].str.contains('核心指标✓').sum()
        ma20_satisfied = potential_stocks['评分详情'].str.contains('MA20✓').sum()
        ema10_satisfied = potential_stocks['评分详情'].str.contains('EMA10✓').sum()
        ema5_satisfied = potential_stocks['评分详情'].str.contains('EMA5✓').sum()
        ema20_satisfied = potential_stocks['评分详情'].str.contains('EMA20✓').sum()
        
        total = len(potential_stocks)
        print(f"  核心指标满足率: {core_satisfied}/{total} ({core_satisfied/total*100:.1f}%)")
        print(f"  MA20满足率: {ma20_satisfied}/{total} ({ma20_satisfied/total*100:.1f}%)")
        print(f"  EMA10满足率: {ema10_satisfied}/{total} ({ema10_satisfied/total*100:.1f}%)")
        print(f"  EMA5满足率: {ema5_satisfied}/{total} ({ema5_satisfied/total*100:.1f}%)")
        print(f"  EMA20满足率: {ema20_satisfied}/{total} ({ema20_satisfied/total*100:.1f}%)")


def example_batch_processing():
    """示例3：批量处理（演示用）"""
    print("\n" + "=" * 60)
    print("示例3：批量处理演示")
    print("=" * 60)
    
    # 注意：这里只是演示批量处理的用法
    # 实际使用时需要提供真实的文件列表
    
    print("批量处理功能演示：")
    print("```python")
    print("# 准备文件列表")
    print("file_list = [")
    print("    'data1.csv',")
    print("    'data2.csv',")
    print("    'data3.csv'")
    print("]")
    print("")
    print("# 执行批量评分")
    print("results = batch_score_files(file_list, output_dir='scored_results/')")
    print("")
    print("# 查看处理结果")
    print("for file_path, status in results.items():")
    print("    print(f'{file_path}: {status}')")
    print("```")
    
    print("\n批量处理的优势：")
    print("- 自动处理多个文件")
    print("- 统一的输出目录管理")
    print("- 详细的处理状态报告")
    print("- 错误处理和恢复机制")


def example_score_interpretation():
    """示例4：评分结果解读"""
    print("\n" + "=" * 60)
    print("示例4：评分结果解读指南")
    print("=" * 60)
    
    print("评分等级解读：")
    print("")
    print("🏆 优秀（90-100分）：")
    print("   - 满足核心指标 + 3-4个辅助指标")
    print("   - 技术面非常强势，短期上涨概率高")
    print("   - 适合激进型投资者")
    print("")
    print("⭐ 良好（80-89分）：")
    print("   - 满足核心指标 + 2-3个辅助指标")
    print("   - 技术面较强，具有上涨潜力")
    print("   - 适合平衡型投资者")
    print("")
    print("📈 中等（70-79分）：")
    print("   - 满足核心指标 + 1-2个辅助指标")
    print("   - 技术面一般，需要观察")
    print("   - 适合谨慎型投资者")
    print("")
    print("✅ 及格（50-69分）：")
    print("   - 仅满足核心指标")
    print("   - 基本面可能不错，但技术面偏弱")
    print("   - 可作为长期关注对象")
    print("")
    print("❌ 不合格（0分）：")
    print("   - 不满足核心指标")
    print("   - 技术面较弱，不建议短期投资")
    print("   - 需要等待更好的入场时机")
    
    print("\n投资建议：")
    print("- 评分仅供参考，需结合基本面分析")
    print("- 高分股票适合短期技术面交易")
    print("- 中等分数股票可关注中长期机会")
    print("- 建议分散投资，控制风险")


def main():
    """主函数"""
    print("股票技术指标评分系统使用示例")
    print("基于验证的核心指标'i1_最高_收盘比例'和最佳辅助指标组合")
    
    try:
        # 示例1：单文件评分
        example_single_file_scoring()
        
        # 示例2：自定义分析
        example_custom_analysis()
        
        # 示例3：批量处理演示
        example_batch_processing()
        
        # 示例4：评分解读
        example_score_interpretation()
        
        print("\n" + "=" * 60)
        print("所有示例演示完成！")
        print("=" * 60)
        print("💡 提示：")
        print("- 可以修改评分阈值来适应不同的市场环境")
        print("- 建议定期重新评估和调整参数")
        print("- 评分系统可以与其他分析工具结合使用")
        print("- 投资有风险，评分仅供参考")
        
    except Exception as e:
        print(f"示例运行出错：{str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 执行完成命令
        import subprocess
        try:
            subprocess.run(['echo', '搞完了'], shell=True, check=True)
        except:
            print("搞完了")


if __name__ == "__main__":
    main()
