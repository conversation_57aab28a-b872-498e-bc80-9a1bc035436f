#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查large_buy_orders表结构和数据范围问题
"""

import pandas as pd
import pymysql
from sqlalchemy import create_engine, text
import logging
import os
import datetime

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': 'stock-31bk-stock-31bankuai.c.aivencloud.com',
    'port': 26991,
    'user': 'avnadmin',
    'password': 'AVNS_daq_tCJ6LP2VwbS_633',
    'database': 'defaultdb',
    'charset': 'utf8mb4'
}

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_database_engine():
    """创建数据库引擎"""
    try:
        connection_url = (
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}"
            f"@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['database']}"
            f"?charset={MYSQL_CONFIG['charset']}&ssl_verify_cert=false&ssl_verify_identity=false"
        )

        engine = create_engine(
            connection_url,
            pool_size=5,
            max_overflow=10,
            pool_timeout=10,
            pool_recycle=3600,
            pool_pre_ping=True,
            echo=False,
            connect_args={
                'connect_timeout': 10,
                'read_timeout': 60,
                'write_timeout': 60,
                'charset': 'utf8mb4'
            }
        )

        # 测试连接
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.fetchone():
                logger.info("✅ 数据库连接成功")
                return engine
            else:
                raise Exception("数据库连接测试失败")

    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        raise

def check_table_structure():
    """检查表结构"""
    try:
        engine = create_database_engine()
        
        # 检查表结构
        logger.info("🔍 检查large_buy_orders表结构...")
        with engine.connect() as conn:
            result = conn.execute(text("DESCRIBE large_buy_orders"))
            columns = result.fetchall()
            
            print("\n📋 表结构信息:")
            print("=" * 80)
            for col in columns:
                field_name = col[0]
                field_type = col[1]
                is_null = col[2]
                key = col[3]
                default = col[4]
                extra = col[5]
                print(f"字段: {field_name:20} | 类型: {field_type:20} | 允许NULL: {is_null:5} | 键: {key:5} | 默认值: {default}")
                
                # 特别关注negMarketValue字段
                if field_name == 'negMarketValue':
                    print(f"🎯 关键字段 negMarketValue: {field_type}")
                    
                    # 分析数据类型范围
                    if 'int(' in field_type.lower():
                        print("   📊 INT类型范围: -2,147,483,648 到 2,147,483,647")
                    elif 'bigint' in field_type.lower():
                        print("   📊 BIGINT类型范围: -9,223,372,036,854,775,808 到 9,223,372,036,854,775,807")
                    elif 'decimal' in field_type.lower() or 'numeric' in field_type.lower():
                        print(f"   📊 DECIMAL/NUMERIC类型: {field_type}")
                    elif 'float' in field_type.lower():
                        print("   📊 FLOAT类型: 单精度浮点数")
                    elif 'double' in field_type.lower():
                        print("   📊 DOUBLE类型: 双精度浮点数")
            
            print("=" * 80)
            
        engine.dispose()
        
    except Exception as e:
        logger.error(f"❌ 检查表结构失败: {e}")

def analyze_csv_data():
    """分析CSV数据中的negMarketValue范围"""
    try:
        # 查找最新的CSV文件
        predictDate = datetime.date.today().strftime("%Y%m%d")
        csv_file_path = rf'd:\Andy\gupiao\大笔买入{predictDate}_sum.csv'
        
        if not os.path.exists(csv_file_path):
            # 尝试昨天的文件
            predictDate = (datetime.date.today() - datetime.timedelta(days=1)).strftime("%Y%m%d")
            csv_file_path = rf'd:\Andy\gupiao\大笔买入{predictDate}_sum.csv'
        
        if os.path.exists(csv_file_path):
            logger.info(f"📊 分析CSV文件: {csv_file_path}")
            df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
            
            if 'negMarketValue' in df.columns:
                neg_market_values = df['negMarketValue'].dropna()
                
                print("\n📈 negMarketValue字段数据分析:")
                print("=" * 60)
                print(f"总记录数: {len(df)}")
                print(f"negMarketValue非空记录数: {len(neg_market_values)}")
                print(f"最小值: {neg_market_values.min():,.0f}")
                print(f"最大值: {neg_market_values.max():,.0f}")
                print(f"平均值: {neg_market_values.mean():,.0f}")
                print(f"中位数: {neg_market_values.median():,.0f}")
                
                # 检查超出INT范围的值
                int_max = 2147483647
                int_min = -2147483648
                
                over_int_max = neg_market_values[neg_market_values > int_max]
                under_int_min = neg_market_values[neg_market_values < int_min]
                
                print(f"\n🚨 数据范围问题:")
                print(f"超出INT最大值({int_max:,})的记录数: {len(over_int_max)}")
                print(f"低于INT最小值({int_min:,})的记录数: {len(under_int_min)}")
                
                if len(over_int_max) > 0:
                    print(f"超出范围的最大值: {over_int_max.max():,.0f}")
                    print(f"超出范围的示例值:")
                    for i, val in enumerate(over_int_max.head(5)):
                        print(f"  {i+1}. {val:,.0f}")
                
                print("=" * 60)
                
                # 建议的数据类型
                max_val = neg_market_values.max()
                if max_val <= int_max and neg_market_values.min() >= int_min:
                    print("✅ 建议数据类型: INT")
                elif max_val <= 9223372036854775807:
                    print("🔧 建议数据类型: BIGINT")
                else:
                    print("🔧 建议数据类型: DECIMAL(20,2) 或 DOUBLE")
                    
            else:
                print("⚠️ CSV文件中未找到negMarketValue字段")
                print("可用字段:", list(df.columns))
        else:
            print(f"❌ 未找到CSV文件: {csv_file_path}")
            
    except Exception as e:
        logger.error(f"❌ 分析CSV数据失败: {e}")

def generate_fix_sql():
    """生成修复SQL语句"""
    print("\n🔧 修复方案SQL语句:")
    print("=" * 60)
    print("-- 方案A: 修改negMarketValue字段为BIGINT类型")
    print("ALTER TABLE large_buy_orders MODIFY COLUMN negMarketValue BIGINT;")
    print()
    print("-- 方案B: 修改negMarketValue字段为DECIMAL类型（推荐用于金融数据）")
    print("ALTER TABLE large_buy_orders MODIFY COLUMN negMarketValue DECIMAL(20,2);")
    print()
    print("-- 方案C: 修改negMarketValue字段为DOUBLE类型")
    print("ALTER TABLE large_buy_orders MODIFY COLUMN negMarketValue DOUBLE;")
    print("=" * 60)

if __name__ == "__main__":
    print("🔍 开始检查数据库表结构和数据范围...")
    
    # 检查表结构
    check_table_structure()
    
    # 分析CSV数据
    analyze_csv_data()
    
    # 生成修复SQL
    generate_fix_sql()
    
    print("\n✅ 检查完成！")
