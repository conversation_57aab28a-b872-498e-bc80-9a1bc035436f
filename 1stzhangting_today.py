﻿import os
import pandas as pd
from datetime import datetime, timedelta
import requests

token = '44055711dfeb4124a8df9531f8dd2f59'  # 在pushplus网站中可以找到
def send_message(title, message):
    """发送消息到pushplus和ntfy.sh"""
    try:
        # requests.post("https://ntfy.sh/Xiaoertan", data=message.encode(encoding='utf-8'))
        url = f'http://www.pushplus.plus/send?token={token}&title={title}&content={message}'
        requests.get(url)
    except Exception as e:
        print("发送消息时发生错误:", e)

def load_data_efficiently(date):
    """只读取必要的列，减少内存使用"""
    needed_columns = {
        'equd': ['secID', 'secShortName', 'ticker', 'exchangeCD', 'chgPct', 'closePrice',
                 'lowestPrice', 'highestPrice', 'preClosePrice', 'actPreClosePrice', 'turnoverVol', 'turnoverRate', 'openPrice'],
        'limit': ['secID', 'secShortName', 'limitUpPrice', 'limitDownPrice'],
        'factors': ['secID', 'MA5', 'MA10', 'MA20', 'MA60', 'EMA5', 'EMA10', 'EMA20', 'EMA60', 'RSI', 'KDJ_K', 'KDJ_D', 'KDJ_J', 'OBV', 'OBV6', 'OBV20', 'MACD', 'VEMA5', 'VEMA10', 'VEMA12', 'VEMA26', 'BollUp', 'BollDown', 'ATR6', 'ATR14', 'PSY', 'AR', 'BR', 'VR', 'BIAS5', 'BIAS10', 'BIAS20', 'BIAS60', 'ROC6', 'ROC20', 'EMV6', 'EMV14', 'MTM', 'MTMMA', 'PVT', 'PVT6', 'PVT12', 'TRIX5', 'TRIX10', 'MFI', 'WVAD', 'ChaikinOscillator', 'ChaikinVolatility', 'ASI', 'ARBR', 'CR20', 'ADTM', 'DDI', 'DEA', 'DIFF', 'BBI', 'TRIX5', 'TRIX10', 'UOS', 'MA10RegressCoeff12', 'MA10RegressCoeff6']
    }


    try:
        equd_df = pd.read_csv(os.path.join(equd_dir, f"{date}.csv"),
                             encoding='gbk', usecols=needed_columns['equd'])
        limit_df = pd.read_csv(os.path.join(limit_dir, f"{date}.csv"),
                              encoding='gbk', usecols=needed_columns['limit'])
        factors_df = pd.read_csv(os.path.join(mkt_stock_factors_dir, f"{date}.csv"),
                                encoding='gbk', usecols=needed_columns['factors'])
        return pd.merge(pd.merge(equd_df, limit_df, on=["secID", "secShortName"]),
                       factors_df, on=["secID"])
    except Exception as e:
        print(f"读取{date}数据出错: {e}")
        return None

# 定义打分函数
def score_data(row):
    score = 0

    # 满足任何一个条件，加分
    if '涨停当天_BR_ratio' in row and row['涨停当天_BR_ratio'] > 2.0:
        score += 1
    if '涨停当天_ATR6_ratio' in row and row['涨停当天_ATR6_ratio'] < 1.0:
        score += 1
    if '涨停当天_PSY_ratio' in row and row['涨停当天_PSY_ratio'] > 2.0:
        score += 1
    if '涨停当天_EMA5_ratio' in row and row['涨停当天_EMA5_ratio'] < 1.0:
        score += 1
    if '涨停当天_ATR14_ratio' in row and row['涨停当天_ATR14_ratio'] < 1.0:
        score += 1

    # 不满足任何一个条件，加分
    if not ('涨停当天_ATR14_ratio' in row and row['涨停当天_ATR14_ratio'] > 1.5):
        score += 0.5
    if not ('涨停当天_OBV6_ratio' in row and row['涨停当天_OBV6_ratio'] > 2.0):
        score += 0.5
    if not ('涨停当天_PVT12_ratio' in row and row['涨停当天_PVT12_ratio'] < 0.5):
        score += 0.5
    if not ('涨停当天_OBV20_ratio' in row and row['涨停当天_OBV20_ratio'] > 2.0):
        score += 0.5
    if not ('涨停当天_ATR6_ratio' in row and row['涨停当天_ATR6_ratio'] > 2.0):
        score += 0.5
    if not ('涨停当天_PVT6_ratio' in row and row['涨停当天_PVT6_ratio'] < 0.5):
        score += 0.5
    if not ('涨停当天_OBV_ratio' in row and row['涨停当天_OBV_ratio'] > 2.0):
        score += 0.5
    if not ('涨停当天_PVT_ratio' in row and row['涨停当天_PVT_ratio'] > 2.0):
        score += 0.5
    if not ('涨停当天_turnoverRate_ratio' in row and row['涨停当天_turnoverRate_ratio'] < 0.5):
        score += 0.5
    if not ('涨停当天_turnoverVol_ratio' in row and row['涨停当天_turnoverVol_ratio'] < 0.5):
        score += 0.5
    if not ('涨停当天_AR_ratio' in row and row['涨停当天_AR_ratio'] < 1.0):
        score += 0.5
    if not ('涨停当天_TRIX10_ratio' in row and row['涨停当天_TRIX10_ratio'] > 2.0):
        score += 0.5
    if not ('涨停当天_DEA_ratio' in row and row['涨停当天_DEA_ratio'] > 2.0):
        score += 0.5
    if not ('涨停当天_CR20_ratio' in row and row['涨停当天_CR20_ratio'] > 2.0):
        score += 0.5
    if not ('涨停当天_BR_ratio' in row and row['涨停当天_BR_ratio'] > 1.5):
        score += 0.5
    if not ('涨停当天_VEMA26_ratio' in row and row['涨停当天_VEMA26_ratio'] > 2.0):
        score += 0.5
    if not ('涨停当天_MA10RegressCoeff6_ratio' in row and row['涨停当天_MA10RegressCoeff6_ratio'] > 2.0):
        score += 0.5
    if not ('涨停当天_ARBR_ratio' in row and row['涨停当天_ARBR_ratio'] > 2.0):
        score += 0.5
    if not ('涨停当天_TRIX5_ratio' in row and row['涨停当天_TRIX5_ratio'] > 1.5):
        score += 0.5
    if not ('涨停当天_DIFF_ratio' in row and row['涨停当天_DIFF_ratio'] > 2.0):
        score += 0.5
    if not ('涨停当天_BIAS10_ratio' in row and row['涨停当天_BIAS10_ratio'] > 1.0):
        score += 0.5
    if not ('涨停当天_VEMA5_ratio' in row and row['涨停当天_VEMA5_ratio'] < 1.0):
        score += 0.5
    if not ('涨停当天_ROC6_ratio' in row and row['涨停当天_ROC6_ratio'] > 1.5):
        score += 0.5
    if not ('涨停当天_MTMMA_ratio' in row and row['涨停当天_MTMMA_ratio'] > 2.0):
        score += 0.5
    if not ('涨停当天_BIAS5_ratio' in row and row['涨停当天_BIAS5_ratio'] > 1.0):
        score += 0.5
    if not ('涨停当天_EMV6_ratio' in row and row['涨停当天_EMV6_ratio'] > 1.0):
        score += 0.5
    if not ('涨停当天_BIAS20_ratio' in row and row['涨停当天_BIAS20_ratio'] > 1.0):
        score += 0.5
    if not ('涨停当天_ChaikinOscillator_ratio' in row and row['涨停当天_ChaikinOscillator_ratio'] > 1.0):
        score += 0.5
    if not ('涨停当天_ASI_ratio' in row and row['涨停当天_ASI_ratio'] > 1.0):
        score += 0.5
    if not ('涨停当天_VEMA10_ratio' in row and row['涨停当天_VEMA10_ratio'] < 1.0):
        score += 0.5
    if not ('涨停当天_MTM_ratio' in row and row['涨停当天_MTM_ratio'] > 1.5):
        score += 0.5
    if not ('涨停当天_VEMA12_ratio' in row and row['涨停当天_VEMA12_ratio'] < 1.0):
        score += 0.5
    if not ('涨停当天_KDJ_J_ratio' in row and row['涨停当天_KDJ_J_ratio'] < 1.5):
        score += 0.5
    if not ('涨停当天_EMV14_ratio' in row and row['涨停当天_EMV14_ratio'] > 2.0):
        score += 0.5
    if not ('涨停当天_BIAS60_ratio' in row and row['涨停当天_BIAS60_ratio'] > 1.0):
        score += 0.5
    if not ('涨停当天_UOS_ratio' in row and row['涨停当天_UOS_ratio'] < 1.0):
        score += 0.5
    if not ('涨停当天_MACD_ratio' in row and row['涨停当天_MACD_ratio'] > 1.0):
        score += 0.5
    if not ('涨停当天_ROC20_ratio' in row and row['涨停当天_ROC20_ratio'] > 1.0):
        score += 0.5
    if not ('涨停当天_DDI_ratio' in row and row['涨停当天_DDI_ratio'] > 1.0):
        score += 0.5
    if not ('涨停当天_KDJ_K_ratio' in row and row['涨停当天_KDJ_K_ratio'] < 1.5):
        score += 0.5

    return score

# 定义条件判断函数
def check_conditions(row):
    # 满足任何一个条件
    condition1 = False
    if '涨停当天_BR_ratio' in row and row['涨停当天_BR_ratio'] > 2.0:
        condition1 = True
    elif '涨停当天_ATR6_ratio' in row and row['涨停当天_ATR6_ratio'] < 1.0:
        condition1 = True
    elif '涨停当天_PSY_ratio' in row and row['涨停当天_PSY_ratio'] > 2.0:
        condition1 = True
    elif '涨停当天_EMA5_ratio' in row and row['涨停当天_EMA5_ratio'] < 1.0:
        condition1 = True
    elif '涨停当天_ATR14_ratio' in row and row['涨停当天_ATR14_ratio'] < 1.0:
        condition1 = True

    # 不满足任何一个条件
    condition2 = True
    if '涨停当天_ATR14_ratio' in row and row['涨停当天_ATR14_ratio'] > 1.5:
        condition2 = False
    elif '涨停当天_OBV6_ratio' in row and row['涨停当天_OBV6_ratio'] > 2.0:
        condition2 = False
    elif '涨停当天_PVT12_ratio' in row and row['涨停当天_PVT12_ratio'] < 0.5:
        condition2 = False
    elif '涨停当天_OBV20_ratio' in row and row['涨停当天_OBV20_ratio'] > 2.0:
        condition2 = False
    elif '涨停当天_ATR6_ratio' in row and row['涨停当天_ATR6_ratio'] > 2.0:
        condition2 = False
    elif '涨停当天_PVT6_ratio' in row and row['涨停当天_PVT6_ratio'] < 0.5:
        condition2 = False
    elif '涨停当天_OBV_ratio' in row and row['涨停当天_OBV_ratio'] > 2.0:
        condition2 = False
    elif '涨停当天_PVT_ratio' in row and row['涨停当天_PVT_ratio'] > 2.0:
        condition2 = False
    elif '涨停当天_turnoverRate_ratio' in row and row['涨停当天_turnoverRate_ratio'] < 0.5:
        condition2 = False
    elif '涨停当天_turnoverVol_ratio' in row and row['涨停当天_turnoverVol_ratio'] < 0.5:
        condition2 = False
    elif '涨停当天_AR_ratio' in row and row['涨停当天_AR_ratio'] < 1.0:
        condition2 = False
    elif '涨停当天_TRIX10_ratio' in row and row['涨停当天_TRIX10_ratio'] > 2.0:
        condition2 = False
    elif '涨停当天_DEA_ratio' in row and row['涨停当天_DEA_ratio'] > 2.0:
        condition2 = False
    elif '涨停当天_CR20_ratio' in row and row['涨停当天_CR20_ratio'] > 2.0:
        condition2 = False
    elif '涨停当天_BR_ratio' in row and row['涨停当天_BR_ratio'] > 1.5:
        condition2 = False
    elif '涨停当天_VEMA26_ratio' in row and row['涨停当天_VEMA26_ratio'] > 2.0:
        condition2 = False
    elif '涨停当天_MA10RegressCoeff6_ratio' in row and row['涨停当天_MA10RegressCoeff6_ratio'] > 2.0:
        condition2 = False
    elif '涨停当天_ARBR_ratio' in row and row['涨停当天_ARBR_ratio'] > 2.0:
        condition2 = False
    elif '涨停当天_TRIX5_ratio' in row and row['涨停当天_TRIX5_ratio'] > 1.5:
        condition2 = False
    elif '涨停当天_DIFF_ratio' in row and row['涨停当天_DIFF_ratio'] > 2.0:
        condition2 = False
    elif '涨停当天_BIAS10_ratio' in row and row['涨停当天_BIAS10_ratio'] > 1.0:
        condition2 = False
    elif '涨停当天_VEMA5_ratio' in row and row['涨停当天_VEMA5_ratio'] < 1.0:
        condition2 = False
    elif '涨停当天_ROC6_ratio' in row and row['涨停当天_ROC6_ratio'] > 1.5:
        condition2 = False
    elif '涨停当天_MTMMA_ratio' in row and row['涨停当天_MTMMA_ratio'] > 2.0:
        condition2 = False
    elif '涨停当天_BIAS5_ratio' in row and row['涨停当天_BIAS5_ratio'] > 1.0:
        condition2 = False
    elif '涨停当天_EMV6_ratio' in row and row['涨停当天_EMV6_ratio'] > 1.0:
        condition2 = False
    elif '涨停当天_BIAS20_ratio' in row and row['涨停当天_BIAS20_ratio'] > 1.0:
        condition2 = False
    elif '涨停当天_ChaikinOscillator_ratio' in row and row['涨停当天_ChaikinOscillator_ratio'] > 1.0:
        condition2 = False
    elif '涨停当天_ASI_ratio' in row and row['涨停当天_ASI_ratio'] > 1.0:
        condition2 = False
    elif '涨停当天_VEMA10_ratio' in row and row['涨停当天_VEMA10_ratio'] < 1.0:
        condition2 = False

    # 同时满足两个条件
    return condition1 and condition2

def process_stock_data(group):
    """处理单个股票的数据，选出第一天涨停的股票"""
    if len(group) < 5:  # 至少需要5天数据（涨停当天和涨停前四天）
        return []

    results = []
    group = group.reset_index(drop=True)

    # print(f"\n处理股票: {group.iloc[0]['secShortName']} ({group.iloc[0]['secID']})")
    # print(f"数据天数: {len(group)}")

    found_patterns = 0
    # 从第5天开始，确保有足够的历史数据（涨停前4天）
    for i in range(4, len(group)):
        # 判断是否涨停：收盘价等于涨停价
        if group.loc[i, "closePrice"] == group.loc[i, "limitUpPrice"] :
            # 确保前四天都不是涨停
            if (group.loc[i-1, "closePrice"] != group.loc[i-1, "limitUpPrice"] and
                group.loc[i-2, "closePrice"] != group.loc[i-2, "limitUpPrice"] and
                group.loc[i-3, "closePrice"] != group.loc[i-3, "limitUpPrice"] and
                group.loc[i-4, "closePrice"] != group.loc[i-4, "limitUpPrice"]):
                # 确保有足够的后续数据
                if i <= len(group):
                    # 创建结果字典，包含基本信息
                    result = {
                        "secID": group.iloc[0]["secID"],
                        "secShortName": group.iloc[0]["secShortName"],
                        "涨停日期": group.loc[i, "date"]
                    }

                    # 添加每一天的数据
                    day_names = {
                        0: "涨停当天"
                    }

                    # 添加equd中的列的比值
                    equd_cols = ['turnoverVol', 'turnoverRate']
                    for day_offset, day_name in day_names.items():
                        if day_offset > -4:  # 从涨停前3天开始，因为需要前一天的数据计算比值
                            for col in equd_cols:
                                if col in group.columns:
                                    current_value = group.loc[i+day_offset, col]
                                    prev_value = group.loc[i+day_offset-1, col]
                                    if prev_value != 0 and not pd.isna(current_value) and not pd.isna(prev_value):  # 避免除零错误和NaN
                                        ratio = current_value / prev_value
                                        result[f"{day_name}_{col}_ratio"] = ratio

                    # 添加factors中的列的比值
                    factor_cols = ['ATR6', 'ATR14', 'BR', 'PSY', 'EMA5', 'OBV6', 'PVT12', 'OBV20', 'PVT6', 'OBV', 'PVT',
                                  'AR', 'TRIX10', 'DEA', 'CR20', 'VEMA26', 'MA10RegressCoeff6', 'ARBR', 'TRIX5', 'DIFF',
                                  'BIAS10', 'VEMA5', 'ROC6', 'MTMMA', 'BIAS5', 'EMV6', 'BIAS20', 'ChaikinOscillator',
                                  'ASI', 'VEMA10', 'MTM', 'VEMA12', 'KDJ_J', 'EMV14', 'BIAS60', 'UOS', 'MACD', 'ROC20',
                                  'DDI', 'KDJ_K']
                    for day_offset, day_name in day_names.items():
                        if day_offset > -4:  # 从涨停前3天开始
                            for col in factor_cols:
                                if col in group.columns:
                                    current_value = group.loc[i+day_offset, col]
                                    prev_value = group.loc[i+day_offset-1, col]
                                    if prev_value != 0 and not pd.isna(current_value) and not pd.isna(prev_value):  # 避免除零错误和NaN
                                        ratio = current_value / prev_value
                                        result[f"{day_name}_{col}_ratio"] = ratio

                    # 计算得分
                    result['得分'] = score_data(result)

                    # 判断是否满足条件
                    result['满足条件'] = check_conditions(result)

                    found_patterns += 1
                    results.append(result)

    # print(f"找到符合条件的模式数量: {found_patterns}")
    return results

# 2. 然后是全局变量定义
token = '44055711dfeb4124a8df9531f8dd2f59'
equd_dir = "d:\\Andy\\Data\\MktEqudGet\\"
limit_dir = "d:\\Andy\\Data\\MktLimitGet\\"
mkt_stock_factors_dir = r'd:\Andy\Data\MktStockFactorsOneDayGet'

# 3. 主程序代码
end_date = (datetime.today() - timedelta(days=0)).strftime("%Y%m%d")
start_date = (datetime.strptime(end_date, '%Y%m%d') - timedelta(days=14)).strftime("%Y%m%d")
print("Start date:", start_date)
print("End date:", end_date)
output_file = 'd:\\Andy\\gupiao\\zhangting_today.csv'

# 获取日期范围内的交易日
trading_dates = sorted([f.split('.')[0] for f in os.listdir(equd_dir)
                      if f.endswith('.csv') and start_date <= f.split('.')[0] <= end_date])

print(f"找到交易日数量: {len(trading_dates)}")

# 创建一个空的DataFrame来存储所有日期的数据
all_data = pd.DataFrame()

# 读取所有日期的数据并合并
for date in trading_dates:
    daily_data = load_data_efficiently(date)
    if daily_data is not None:
        daily_data['date'] = date  # 添加日期列
        all_data = pd.concat([all_data, daily_data], ignore_index=True)

# 应用过滤条件
filtered_data = all_data[
    (all_data['ticker'] < 700000) &
    (all_data['exchangeCD'].isin(["XSHE", "XSHG"])) &
    (~all_data['secShortName'].str.contains('B|ST')) &
    (all_data['closePrice'] > 3)
]

all_results = []
# 按股票代码分组，这样每个group就会包含该股票在整个日期范围内的所有数据
for secID, group in filtered_data.groupby("secID"):
    # 按日期排序
    group = group.sort_values('date')
    results = process_stock_data(group)
    all_results.extend(results)

print("\n最终结果统计:")
print(f"找到的总模式数量: {len(all_results)}")

# 将结果转换为DataFrame并保存到CSV文件
results_df = pd.DataFrame(all_results)
if not results_df.empty:
    # 获取最大的反弹日期
    max_date = results_df['涨停日期'].max()
    # 只保留得分大于20或者满足条件的数据
    results_df = results_df[(results_df['得分'] > 20) | (results_df['满足条件'] == True)]
    # 只保留最大日期的数据
    results_df = results_df[results_df['涨停日期'] == max_date]
    # 删除多列
    results_df.drop(['涨停当天_turnoverVol_ratio', '涨停当天_turnoverRate_ratio', '涨停当天_ATR6_ratio', '涨停当天_ATR14_ratio', '涨停当天_BR_ratio', '涨停当天_PSY_ratio', '涨停当天_EMA5_ratio', '涨停当天_OBV6_ratio', '涨停当天_PVT12_ratio', '涨停当天_OBV20_ratio', '涨停当天_PVT6_ratio', '涨停当天_OBV_ratio', '涨停当天_PVT_ratio', '涨停当天_AR_ratio', '涨停当天_TRIX10_ratio', '涨停当天_DEA_ratio', '涨停当天_CR20_ratio', '涨停当天_VEMA26_ratio', '涨停当天_MA10RegressCoeff6_ratio', '涨停当天_ARBR_ratio', '涨停当天_TRIX5_ratio', '涨停当天_DIFF_ratio', '涨停当天_BIAS10_ratio', '涨停当天_VEMA5_ratio', '涨停当天_ROC6_ratio', '涨停当天_MTMMA_ratio', '涨停当天_BIAS5_ratio', '涨停当天_EMV6_ratio', '涨停当天_BIAS20_ratio', '涨停当天_ChaikinOscillator_ratio', '涨停当天_ASI_ratio', '涨停当天_VEMA10_ratio', '涨停当天_MTM_ratio', '涨停当天_VEMA12_ratio', '涨停当天_KDJ_J_ratio', '涨停当天_EMV14_ratio', '涨停当天_BIAS60_ratio', '涨停当天_UOS_ratio', '涨停当天_MACD_ratio', '涨停当天_ROC20_ratio', '涨停当天_DDI_ratio', '涨停当天_KDJ_K_ratio'
], axis=1, inplace=True)
    # 按照 得分 列降序排序
    results_df = results_df.sort_values(by='得分', ascending=False)

    # 合并版块信息
    zhangting_all = pd.read_csv(r'd:\Andy\gupiao\zhangting_all_unique.csv')
    results_df = pd.merge(results_df, zhangting_all, how='left', on=['secID'])
    stock_board_concept_name_ths_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_ths_merged.csv', encoding='utf-8-sig')
    stock_board_concept_name_ths_merged.drop(['secShortName'], axis=1, inplace=True)
    stock_board_concept_name_em_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_em_merged.csv', encoding='utf-8-sig')
    stock_board_concept_name_em_merged['secID'] = stock_board_concept_name_em_merged['代码'].astype(str).str.zfill(6).apply(lambda x: x + '.XSHE' if x.startswith(('00', '30')) else (x + '.XSHG' if x.startswith(('60', '68')) else x))
    stock_board_concept_name_em_merged.drop(['序号','代码','secShortName_x','最新价','涨跌幅', '涨跌额', '成交量', '成交额', '振幅', '最高', '最低', '今开', '昨收', '换手率', '市盈率-动态', '市净率'], axis=1, inplace=True)
    results_df = pd.merge(results_df, stock_board_concept_name_ths_merged, how='left', on=['secID'])
    results_df = pd.merge(results_df, stock_board_concept_name_em_merged, how='left', on=['secID'])

    # # 保存最终结果
    # results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    # upload_file_to_ubuntu(output_file, remote_dir_path, ubuntu_server_ip, ubuntu_username, ubuntu_password)

    #将列“i1_量比_小于0”保留小数点后两位
    # results_df['得分'] = pd.to_numeric(results_df['涨停当天_ATR6_ratio'], errors='coerce').round(2)
    if len(results_df)>0:
                    combined_string = '\n'.join(
                        results_df['secID'].astype(str) + "_" +
                        results_df['secShortName'].astype(str) + "_" +
                        results_df['涨停日期'].astype(str) + "_" +
                        results_df['得分'].astype(str) + "_" +
                        results_df['题材'].astype(str)
                    )
                    send_message("首日涨停", combined_string)
    print("\n最终结果统计:")
    print(f"找到最后日期的总模式数量: {len(results_df)}")
    print(f"结果已保存到: {output_file}")

# 保存最终结果
results_df.to_csv(output_file, index=False, encoding='utf-8-sig')