from playwright.sync_api import sync_playwright
from bs4 import BeautifulSoup
import datetime
import pandas as pd
import csv
import os
import time
from collections import Counter
import akshare as ak
import paramiko
import pymysql
import logging
import sys
from sqlalchemy import create_engine, text, MetaData, Table, Column, String, Integer, Numeric, Date, DateTime, Text, Boolean
from sqlalchemy.dialects.mysql import LONGTEXT
import warnings

# 忽略pandas警告
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': 'stock-31bk-stock-31bankuai.c.aivencloud.com',
    'port': 26991,
    'user': 'avnadmin',
    'password': 'AVNS_daq_tCJ6LP2VwbS_633',
    'database': 'defaultdb',
    'charset': 'utf8mb4'
}

def create_database_engine():
    """创建数据库引擎"""
    try:
        connection_url = (
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}"
            f"@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['database']}"
            f"?charset={MYSQL_CONFIG['charset']}&ssl_verify_cert=false&ssl_verify_identity=false"
        )

        engine = create_engine(
            connection_url,
            pool_size=5,
            max_overflow=10,
            pool_timeout=10,
            pool_recycle=3600,
            pool_pre_ping=True,
            echo=False,
            connect_args={
                'connect_timeout': 10,
                'read_timeout': 60,
                'write_timeout': 60,
                'charset': 'utf8mb4'
            }
        )

        # 测试连接
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.fetchone():
                logger.info("✅ 数据库连接成功")
                return engine
            else:
                raise Exception("数据库连接测试失败")

    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        return None

def truncate_string_field(value, max_length, field_name=""):
    """智能截断字符串字段，确保不超过数据库字段长度限制"""
    if pd.isna(value) or value is None:
        return None

    value_str = str(value).strip()
    if len(value_str) <= max_length:
        return value_str

    # 如果是题材类字段，先尝试去重
    if '题材' in field_name or 'concept' in field_name.lower():
        # 按空格分割，去重，再合并
        concepts = value_str.split()
        unique_concepts = []
        seen = set()
        for concept in concepts:
            if concept not in seen:
                unique_concepts.append(concept)
                seen.add(concept)

        deduped_value = ' '.join(unique_concepts)
        if len(deduped_value) <= max_length:
            return deduped_value

        # 如果去重后仍然太长，则截断并添加省略号
        truncated = deduped_value[:max_length-3] + '...'
        return truncated
    else:
        # 普通字段直接截断并添加省略号
        truncated = value_str[:max_length-3] + '...'
        return truncated

def create_zhangting_table(engine):
    """创建涨停股数据表"""
    try:
        table_name = 'zhangting'
        logger.info(f"🔧 正在创建表结构: {table_name}")

        # 删除现有表
        with engine.connect() as conn:
            conn.execute(text(f"DROP TABLE IF EXISTS {table_name}"))
            conn.commit()
            logger.info(f"🗑️ 已删除现有表: {table_name}")

        # 创建表结构
        metadata = MetaData()
        table = Table(
            table_name, metadata,
            Column('id', Integer, primary_key=True, autoincrement=True, comment='主键ID'),
            Column('secID', String(20), comment='证券代码'),
            Column('name', String(50), comment='股票名称'),
            Column('first_time', String(20), comment='首次涨停时间'),
            Column('last_time', String(20), comment='最后涨停时间'),
            Column('price', Numeric(10, 4), comment='价格'),
            Column('consecutive_days', Integer, comment='连板天数'),
            Column('increase_pct', Numeric(8, 4), comment='涨幅百分比'),
            Column('concept', String(1000), comment='题材概念'),
            Column('reason', String(1000), comment='涨停原因'),
            Column('concept_detail', String(2000), comment='题材详情'),
            Column('created_at', DateTime, default=datetime.datetime.now, comment='创建时间'),
            Column('updated_at', DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间')
        )

        metadata.create_all(engine)
        logger.info(f"✅ 表结构创建成功: {table_name}")
        return table

    except Exception as e:
        logger.error(f"❌ 创建表结构失败: {e}")
        raise

def create_concept_stats_table(engine, table_name):
    """创建题材统计表"""
    try:
        logger.info(f"🔧 正在创建表结构: {table_name}")

        # 删除现有表
        with engine.connect() as conn:
            conn.execute(text(f"DROP TABLE IF EXISTS {table_name}"))
            conn.commit()
            logger.info(f"🗑️ 已删除现有表: {table_name}")

        # 创建表结构
        metadata = MetaData()
        table = Table(
            table_name, metadata,
            Column('id', Integer, primary_key=True, autoincrement=True, comment='主键ID'),
            Column('concept', String(100), comment='题材名称'),
            Column('count_current', Integer, comment='当前涨停股出现次数'),
            Column('count_previous', Integer, comment='上次涨停股出现次数'),
            Column('change_from_previous', Integer, comment='比上次次数升降'),
            Column('count_initial', Integer, comment='初始涨停股出现次数'),
            Column('change_from_initial', Integer, comment='比初始次数升降'),
            Column('created_at', DateTime, default=datetime.datetime.now, comment='创建时间'),
            Column('updated_at', DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间')
        )

        metadata.create_all(engine)
        logger.info(f"✅ 表结构创建成功: {table_name}")
        return table

    except Exception as e:
        logger.error(f"❌ 创建表结构失败: {e}")
        raise

def import_zhangting_data_to_mysql(engine, df):
    """将涨停股数据导入到MySQL"""
    try:
        table_name = 'zhangting'
        logger.info(f"📤 开始导入涨停股数据到表: {table_name}")

        if len(df) == 0:
            logger.warning("⚠️ 没有数据需要导入")
            return 0

        # 创建表结构
        create_zhangting_table(engine)

        # 准备数据
        df_clean = df.copy()

        # 字段映射和数据清理
        import_data = []
        for _, row in df_clean.iterrows():
            try:
                record = {
                    'secID': truncate_string_field(row.get('secID', ''), 20),
                    'name': truncate_string_field(row.get('名称', ''), 50),
                    'first_time': truncate_string_field(row.get('首次', ''), 20),
                    'last_time': truncate_string_field(row.get('最后', ''), 20),
                    'price': float(row.get('价格', 0)) if pd.notna(row.get('价格')) else None,
                    'consecutive_days': int(row.get('连板', 0)) if pd.notna(row.get('连板')) else None,
                    'increase_pct': float(row.get('涨幅%', 0)) if pd.notna(row.get('涨幅%')) else None,
                    'concept': truncate_string_field(row.get('题材', ''), 1000, '题材'),
                    'reason': truncate_string_field(row.get('涨停原因', ''), 1000),
                    'concept_detail': truncate_string_field(row.get('题材（G）', ''), 2000, '题材'),
                    'created_at': datetime.datetime.now(),
                    'updated_at': datetime.datetime.now()
                }
                import_data.append(record)
            except Exception as e:
                logger.warning(f"⚠️ 处理行数据失败: {e}")
                continue

        if not import_data:
            logger.warning("⚠️ 没有有效数据可导入")
            return 0

        # 批量导入数据
        import_df = pd.DataFrame(import_data)
        batch_size = 50
        total_rows = len(import_df)
        imported_rows = 0

        for i in range(0, total_rows, batch_size):
            batch_df = import_df.iloc[i:i+batch_size]
            try:
                batch_df.to_sql(
                    table_name,
                    engine,
                    if_exists='append',
                    index=False,
                    method='multi'
                )
                imported_rows += len(batch_df)
                logger.info(f"📊 已导入 {imported_rows}/{total_rows} 行涨停股数据 ({imported_rows/total_rows*100:.1f}%)")
            except Exception as e:
                logger.warning(f"⚠️ 批次导入失败，尝试逐行导入: {e}")
                for _, row in batch_df.iterrows():
                    try:
                        row_df = pd.DataFrame([row])
                        row_df.to_sql(
                            table_name,
                            engine,
                            if_exists='append',
                            index=False,
                            method='multi'
                        )
                        imported_rows += 1
                    except Exception as row_e:
                        logger.error(f"❌ 行导入失败: {row_e}")
                        continue

        logger.info(f"✅ 涨停股数据导入完成！共导入 {imported_rows} 行数据")
        return imported_rows

    except Exception as e:
        logger.error(f"❌ 涨停股数据导入失败: {e}")
        return 0

def import_concept_stats_to_mysql(engine, df, table_name):
    """将题材统计数据导入到MySQL"""
    try:
        logger.info(f"📤 开始导入题材统计数据到表: {table_name}")

        if len(df) == 0:
            logger.warning("⚠️ 没有数据需要导入")
            return 0

        # 创建表结构
        create_concept_stats_table(engine, table_name)

        # 准备数据
        import_data = []
        for _, row in df.iterrows():
            try:
                record = {
                    'concept': truncate_string_field(row.get('题材', ''), 100),
                    'count_current': int(row.get('涨停股出现次数', 0)) if pd.notna(row.get('涨停股出现次数')) else 0,
                    'count_previous': int(row.get('涨停股出现次数_上次', 0)) if pd.notna(row.get('涨停股出现次数_上次')) else 0,
                    'change_from_previous': int(row.get('比上次次数升降', 0)) if pd.notna(row.get('比上次次数升降')) else 0,
                    'count_initial': int(row.get('涨停股出现次数_初始', 0)) if pd.notna(row.get('涨停股出现次数_初始')) else 0,
                    'change_from_initial': int(row.get('比初始次数升降', 0)) if pd.notna(row.get('比初始次数升降')) else 0,
                    'created_at': datetime.datetime.now(),
                    'updated_at': datetime.datetime.now()
                }
                import_data.append(record)
            except Exception as e:
                logger.warning(f"⚠️ 处理行数据失败: {e}")
                continue

        if not import_data:
            logger.warning("⚠️ 没有有效数据可导入")
            return 0

        # 批量导入数据
        import_df = pd.DataFrame(import_data)
        batch_size = 50
        total_rows = len(import_df)
        imported_rows = 0

        for i in range(0, total_rows, batch_size):
            batch_df = import_df.iloc[i:i+batch_size]
            try:
                batch_df.to_sql(
                    table_name,
                    engine,
                    if_exists='append',
                    index=False,
                    method='multi'
                )
                imported_rows += len(batch_df)
                logger.info(f"📊 已导入 {imported_rows}/{total_rows} 行题材统计数据 ({imported_rows/total_rows*100:.1f}%)")
            except Exception as e:
                logger.warning(f"⚠️ 批次导入失败，尝试逐行导入: {e}")
                for _, row in batch_df.iterrows():
                    try:
                        row_df = pd.DataFrame([row])
                        row_df.to_sql(
                            table_name,
                            engine,
                            if_exists='append',
                            index=False,
                            method='multi'
                        )
                        imported_rows += 1
                    except Exception as row_e:
                        logger.error(f"❌ 行导入失败: {row_e}")
                        continue

        logger.info(f"✅ 题材统计数据导入完成！共导入 {imported_rows} 行数据")
        return imported_rows

    except Exception as e:
        logger.error(f"❌ 题材统计数据导入失败: {e}")
        return 0

# 创建数据库引擎
engine = create_database_engine()
if engine is None:
    logger.error("❌ 无法连接到数据库，程序将继续运行但不会保存到数据库")

current_time = datetime.datetime.now().time()
while current_time < datetime.time(11, 30) or (current_time > datetime.time(13, 00) and current_time < datetime.time(17, 00)):

    # 访问新的网页
    url = "http://www.wuylh.com/zthelper/ztindex.jsp"

    try:
        # 使用Playwright启动Edge浏览器
        with sync_playwright() as p:
            # 启动Edge浏览器（无头模式）
            browser = p.chromium.launch(
                headless=True,
                args=[
                    "--disable-gpu",
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-extensions",
                    "--disable-background-networking",
                    "--disable-sync",
                    "--disable-translate",
                    "--ignore-ssl-errors-on-localhost",
                    "--ignore-certificate-errors"
                ]
            )

            # 创建新的页面
            page = browser.new_page()

            # 访问网页
            page.goto(url)
            time.sleep(6)

            # 等待表格加载完成
            page.wait_for_selector("#limituptable", timeout=20000)

            # 等待加载提示消失
            page.wait_for_selector("#loading", state="hidden", timeout=20000)

            # 额外等待一些时间，确保数据完全加载
            time.sleep(5)

            # 获取页面源代码
            page_source = page.content()

            # 关闭浏览器
            browser.close()

        # 使用BeautifulSoup解析页面源代码
        soup = BeautifulSoup(page_source, 'html.parser')
    
        # 提取表格数据
        table = soup.find('table', id='limituptable')
        if table:
            rows = table.find_all('tr')
    
            # 准备CSV文件
            filename = r'D:\Andy\gupiao\zhangting.csv'
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入表头
                headers = [th.text.strip() for th in rows[0].find_all('th')]
                #headers = headers[1:]  # 移除第一个元素（假设它是 "No." 列）
                writer.writerow(headers)
                # print(headers)
                
                # 写入数据行
                for row in rows[1:]:
                    data = [td.text.strip() for td in row.find_all('td')]
                    if data:  # 只有当数据行非空时才处理
                        data = data[:-1]  # 移除第一个元素（与表头处理保持一致）
                        writer.writerow(data)
            # 读取CSV文件
            df = pd.read_csv(filename, encoding='utf-8-sig')
            if len(df) > 0:  
                
                # 要删除的列名列表
                columns_to_remove = ['No.', '资讯', '金额(亿)', '一年', '流通市值', '机构', '今竞%', '今竞', '隔夜单', '次竞%', '次竞', '次竞量', '今竞封单', '次竞封单', '次收', '次高']
                
                # 删除指定的列
                df = df.drop(columns=columns_to_remove, errors='ignore')
                # 只保留"连板"大于0的行
                df = df[df['连板'].astype(int) > 0]
                
                # 重置索引
                df = df.reset_index(drop=True)
                # 将"名 称"列按空格拆分为"secID"和"名称"两列
                df[['secID', '名称']] = df['名 称'].str.split(expand=True)
                # 根据secID的开头添加后缀
                df['secID'] = df['secID'].apply(lambda x: x + '.XSHE' if x.startswith(('00', '30')) else (x + '.XSHG' if x.startswith(('60', '68')) else x))
                # 删除原来的"名 称"列
                df = df.drop(columns=['名 称'])
                
                #####
                MktLimitGet = pd.read_csv(r'd:\Andy\gupiao\MktLimitGet.csv', encoding='GBK')
                MktLimitGet = MktLimitGet[['secID', 'limitUpPrice']]
                dfo_zhangting = df[['secID', '名称', '价格']]
                dfo_zhangting=pd.merge(dfo_zhangting, MktLimitGet, how='left', on=['secID'])
                dfo_zhangting = dfo_zhangting[dfo_zhangting['价格'] == dfo_zhangting['limitUpPrice']]
                #####
                
                if len(dfo_zhangting) > 0:                
                    
                    # 调整列的顺序，将"代码"和"名称"放在最前面
                    columns = df.columns.tolist()
                    columns = ['secID', '名称'] + [col for col in columns if col not in ['secID', '名称']]
                    df = df[columns]
                    # 处理'名称'列
                    df.loc[(df['名称'].str.len() > 4) & (df['名称'].str[-1] == '创'), '名称'] = df['名称'].str[:-1]
                    df = df.rename(columns={col: '涨停原因' for col in df.columns if col.startswith('涨停原因')})

                    # 确保'题材'列为字符串类型，避免数据类型兼容性问题
                    if '题材' in df.columns:
                        df['题材'] = df['题材'].astype('object')

                    # 如果'题材'列为空，则从'涨停原因'列提取内容
                    if '涨停原因' in df.columns:
                        mask = df['题材'].isna()
                        if mask.any():
                            extracted_values = df.loc[mask, '涨停原因'].str.split(n=1).str[0]
                            df.loc[mask, '题材'] = extracted_values
            
                    ####以防万一
                    df_all = pd.read_csv(r'd:\Andy\gupiao\zhangting_all_unique.csv')
                    # 确保df_all的'题材'列也是字符串类型
                    if '题材' in df_all.columns:
                        df_all['题材'] = df_all['题材'].astype('object')

                    # 如果df中'题材'列为空,从df_all中相同secID的'题材'列获取内容
                    mask_gengduo = df['题材'] == '更多...'
                    if mask_gengduo.any():
                        mapped_values = df.loc[mask_gengduo, 'secID'].map(df_all.set_index('secID')['题材'])
                        df.loc[mask_gengduo, '题材'] = mapped_values
                    ####
                    # 将处理后的数据保存回CSV文件
                    df.to_csv(filename, index=False, encoding='utf-8-sig')

                    # 导入涨停股数据到MySQL数据库
                    if engine is not None:
                        try:
                            import_zhangting_data_to_mysql(engine, df)
                        except Exception as e:
                            logger.error(f"❌ 导入涨停股数据到数据库失败: {e}")
                
                concepts_list = df['题材']
                # 使用列表推导式和split()方法分割字符串，并收集所有概念名称
                all_concepts = []
                for concept in concepts_list:
                    if isinstance(concept, str):
                        # 使用split()将字符串按空格分割成列表
                        concepts = concept.strip().split()  # strip()去除首尾空格
                        all_concepts.extend(concepts)  # extend将列表元素添加到all_concepts
                # 使用Counter统计每个概念名称出现的次数
                concept_counts = Counter(all_concepts)
                # 将统计结果转换为DataFrame
                concept_counts_df = pd.DataFrame(list(concept_counts.items()), columns=['题材', '涨停股出现次数'])
                concept_counts_df = concept_counts_df.sort_values(by='涨停股出现次数', ascending=False) #涨停股出现次数
                output_csv = r'D:\Andy\gupiao\zhangting涨停股题材次数统计.csv'
                lasttime_df0925 = pd.read_csv(r'D:\Andy\gupiao\zhangting涨停股题材次数统计0925.csv', encoding='utf-8-sig')
                lasttime_df0925 = lasttime_df0925[['题材', '涨停股出现次数']]
                lasttime_df0925 = lasttime_df0925.rename(columns={'涨停股出现次数': '涨停股出现次数_初始'})
                lasttime_df = pd.read_csv(output_csv, encoding='utf-8-sig')
                lasttime_df = lasttime_df[['题材', '涨停股出现次数']]
                lasttime_df = lasttime_df.rename(columns={'涨停股出现次数': '涨停股出现次数_上次'})
                concept_counts_df = pd.merge(concept_counts_df, lasttime_df, on='题材', how='left')
                # 将涨停股出现次数_上次的空值填充为0
                concept_counts_df['涨停股出现次数_上次'] = concept_counts_df['涨停股出现次数_上次'].fillna(0)
                concept_counts_df['比上次次数升降'] = concept_counts_df['涨停股出现次数'] - concept_counts_df['涨停股出现次数_上次']
                concept_counts_df = pd.merge(concept_counts_df, lasttime_df0925, on='题材', how='left')
                concept_counts_df['涨停股出现次数_初始'] = concept_counts_df['涨停股出现次数_初始'].fillna(0)
                concept_counts_df['比初始次数升降'] = concept_counts_df['涨停股出现次数'] - concept_counts_df['涨停股出现次数_初始']
                concept_counts_df.to_csv(output_csv, index=False, encoding='utf-8-sig')
                # 导入题材统计数据到MySQL数据库
                if engine is not None:
                    try:
                        import_concept_stats_to_mysql(engine, concept_counts_df, 'zhangting_concept_stats')
                    except Exception as e:
                        logger.error(f"❌ 导入题材统计数据到数据库失败: {e}")

                current_time = datetime.datetime.now().time()
                # 修改时间条件：在交易时间内的第一次运行时保存基准数据
                if current_time < datetime.time(9, 30):  # 在9：30前保存基准数据
                    concept_counts_df.to_csv(r'D:\Andy\gupiao\zhangting涨停股题材次数统计0925.csv', index=False, encoding='utf-8-sig')
                    # 导入初始基准数据到MySQL数据库
                    if engine is not None:
                        try:
                            import_concept_stats_to_mysql(engine, concept_counts_df, 'zhangting_concept_stats_0925')
                        except Exception as e:
                            logger.error(f"❌ 导入初始基准数据到数据库失败: {e}")
            
        else:
            print("未找到id为'limituptable'的表格。")
            print("页面内容:")
            print(soup.prettify()[:1000])  # 打印前1000个字符以供检查
    
    except Exception as e:
        print(f"发生错误: {e}")

    print("等待60秒")
    time.sleep(60)