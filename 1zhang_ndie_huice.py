import os
import pandas as pd
from datetime import datetime, timedelta
import requests
import paramiko
import numpy as np

equd_dir = "d:\\Andy\\Data\\MktEqudGet\\"
limit_dir = "d:\\Andy\\Data\\MktLimitGet\\"
mkt_stock_factors_dir = r'd:\Andy\Data\MktStockFactorsOneDayGet'

# 定义日期范围
loop_start = "20250524"
loop_end = "20250530"

# 生成日期序列
current_date = datetime.strptime(loop_start, '%Y%m%d')
end_date_obj = datetime.strptime(loop_end, '%Y%m%d')

while current_date <= end_date_obj:
    end_date = current_date.strftime("%Y%m%d")
    start_date = (current_date - timedelta(days=22)).strftime("%Y%m%d")
    print(f"\n处理日期范围: {start_date} 到 {end_date}")
    output_file = 'N形待选_' + end_date + '.csv'

    # 获取所有交易日的文件名
    all_trading_dates = sorted([f.split('.')[0] for f in os.listdir(equd_dir) if f.endswith('.csv')])
    # 过滤日期范围内的交易日
    trading_dates = [date for date in all_trading_dates if start_date <= date <= end_date]

    # 读取并合并数据
    data = []
    for date in trading_dates:
        equd_file = os.path.join(equd_dir, f"{date}.csv")
        limit_file = os.path.join(limit_dir, f"{date}.csv")
        mkt_stock_factors_file = os.path.join(mkt_stock_factors_dir, f"{date}.csv")

        if not os.path.exists(equd_file) or not os.path.exists(limit_file):
            continue  # 如果当天不是交易日，跳过

        equd_df = pd.read_csv(equd_file, encoding='gbk')
        limit_df = pd.read_csv(limit_file, encoding='gbk')
        mkt_stock_factors_df = pd.read_csv(mkt_stock_factors_file, encoding='gbk')

        merged_df = pd.merge(equd_df, limit_df, on=["secID", "secShortName"])
        merged_df = pd.merge(merged_df, mkt_stock_factors_df, on=["secID"])
        merged_df["date"] = date
        data.append(merged_df)

    if not data:  # 如果没有数据，跳过当前日期
        print(f"日期 {end_date} 没有找到数据，跳过处理")
        current_date += timedelta(days=1)
        continue

    # 合并所有日期的数据
    all_data = pd.concat(data)
    # 过滤不需要的数据
    all_data = all_data[(all_data['ticker_x'] < 700000) &
            ((all_data['exchangeCD_x'] == "XSHE") | (all_data['exchangeCD_x'] == "XSHG")) &
            (~all_data['secShortName'].str.contains('B')) &
            (~all_data['secShortName'].str.contains('ST')) & (all_data['closePrice'] > 1)]

    # 按secID和日期排序
    all_data = all_data.sort_values(by=["secID", "date"])

    # 找到符合条件的记录
    results = []
    total_groups = len(all_data['secID'].unique())
    current_group = 0

    for secID, group in all_data.groupby("secID"):
        current_group += 1
        secShortName = group.iloc[0]["secShortName"]
        group = group.reset_index(drop=True)
        i = 1  # 从第二天开始检查，因为需要检查前一天是否涨停
        while i < len(group) - 2:
            # 检查第1天的涨停，且前一天不是涨停
            chgPct_sum_last5days = group.loc[i-4:i, "chgPct"].sum()
            no_limit_down = all(group.loc[j, "limitDownPrice"] != group.loc[j, "closePrice"] for j in range(i+1, len(group)))
            # 确保涨停后不再出现涨停
            no_more_limit_up = all(group.loc[j, "limitUpPrice"] != group.loc[j, "closePrice"] for j in range(i+1, len(group)))
            turnoverVol_decrease = all(group.loc[j, "turnoverVol"] < group.loc[j-1, "turnoverVol"] for j in range(i+1, len(group)))
            turnoverVol_decrease_days = sum(group.loc[j, "turnoverVol"] < group.loc[j-1, "turnoverVol"] for j in range(i+1, len(group)))
            if (group.loc[i, "limitUpPrice"] == group.loc[i, "closePrice"] and
                ((i-1 >= 0 and group.loc[i-1, "limitUpPrice"] != group.loc[i-1, "closePrice"]) or
                 (i-2 >= 0 and group.loc[i-2, "limitUpPrice"] != group.loc[i-2, "closePrice"])) and
                group.loc[i+1, "limitUpPrice"] != group.loc[i+1, "closePrice"] and
                group.loc[i+2, "limitUpPrice"] != group.loc[i+2, "closePrice"] and
                group.loc[i-4:i, "openPrice"].min() < group.loc[i+1, "lowestPrice"] and
                group.loc[i-4:i, "openPrice"].min() < group.loc[i+2, "lowestPrice"] and
                group.loc[i-2:i, "lowestPrice"].min() < group.loc[i+1:, "lowestPrice"].min() and
                (len(group.loc[i+3:]) == 0 or group.loc[i:i+2, "highestPrice"].max() >= group.loc[i+3:, "highestPrice"].max()) and
                ((group.loc[i, "secID"][:2] in ["00", "60"] and chgPct_sum_last5days < 0.4) or
                 (group.loc[i, "secID"][:2] in ["30", "68"] and chgPct_sum_last5days < 0.6)) and no_limit_down and no_more_limit_up):
                    results.append({
                        "secID": secID,
                        "secShortName": group.loc[i, "secShortName"],
                        "涨停日期": group.loc[i, "date"],
                        "MA5": group.loc[len(group)-1, "MA5"],
                        "MA10": group.loc[len(group)-1, "MA10"],
                        "MA20": group.loc[len(group)-1, "MA20"],
                        "至今最最低价": group.loc[i+1:, "lowestPrice"].min(),
                        '缩量下跌天数': turnoverVol_decrease_days,
                        '持续缩量': "持续缩量" if turnoverVol_decrease else "否",
                        "最后一天收盘价": group["closePrice"].iloc[-1]
                    })
                    i += 3
            else:
                i += 1

    if results:  # 只有当有结果时才处理和保存
        # 转换结果为DataFrame
        results_df = pd.DataFrame(results)
        results_df = results_df.sort_values(by='涨停日期', ascending=False)
        results_df = results_df.drop_duplicates(subset='secShortName', keep='last')
        results_df = results_df[results_df['缩量下跌天数'] > 0]
        # 添加新列，根据下跌第二天最低价与MA5、MA10、MA20的比较结果
        results_df['最低价格位置'] = '5日线上'
        results_df.loc[results_df['至今最最低价'] <= results_df['MA5'], '最低价格位置'] = '5日线以下'
        results_df.loc[results_df['至今最最低价'] <= results_df['MA10'], '最低价格位置'] = '10日线以下'
        results_df.loc[results_df['至今最最低价'] <= results_df['MA20'], '最低价格位置'] = '20日线以下'
        results_df['收盘价格位置'] = '5日线上'
        results_df.loc[results_df['最后一天收盘价'] <= results_df['MA5'], '收盘价格位置'] = '5日线以下'
        results_df.loc[results_df['最后一天收盘价'] <= results_df['MA10'], '收盘价格位置'] = '10日线以下'
        results_df.loc[results_df['最后一天收盘价'] <= results_df['MA20'], '收盘价格位置'] = '20日线以下'
        results_df.drop(['至今最最低价','MA5','MA10','MA20'], axis=1, inplace=True)

        # 合并版块信息
        zhangting_all = pd.read_csv(r'd:\Andy\gupiao\zhangting_all_unique.csv')
        results_df = pd.merge(results_df, zhangting_all, how='left', on=['secID'])
        stock_board_concept_name_ths_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_ths_merged.csv', encoding='utf-8-sig')
        stock_board_concept_name_ths_merged.drop(['secShortName'], axis=1, inplace=True)
        stock_board_concept_name_em_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_em_merged.csv', encoding='utf-8-sig')
        stock_board_concept_name_em_merged['secID'] = stock_board_concept_name_em_merged['代码'].astype(str).str.zfill(6).apply(lambda x: x + '.XSHE' if x.startswith(('00', '30')) else (x + '.XSHG' if x.startswith(('60', '68')) else x))
        stock_board_concept_name_em_merged.drop(['序号','代码','secShortName_x','最新价','涨跌幅', '涨跌额', '成交量', '成交额', '振幅', '最高', '最低', '今开', '昨收', '换手率', '市盈率-动态', '市净率'], axis=1, inplace=True)
        results_df = pd.merge(results_df, stock_board_concept_name_ths_merged, how='left', on=['secID'])
        results_df = pd.merge(results_df, stock_board_concept_name_em_merged, how='left', on=['secID'])

        # 保存结果到CSV文件
        results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"已保存结果到文件: {output_file}")
    else:
        print(f"日期 {end_date} 没有找到符合条件的结果")

    # 移动到下一天
    current_date += timedelta(days=1)