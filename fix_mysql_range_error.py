#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复MySQL数据范围错误的完整解决方案
"""

import pandas as pd
import pymysql
from sqlalchemy import create_engine, text
import logging
import sys
import datetime

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': 'stock-31bk-stock-31bankuai.c.aivencloud.com',
    'port': 26991,
    'user': 'avnadmin',
    'password': 'AVNS_daq_tCJ6LP2VwbS_633',
    'database': 'defaultdb',
    'charset': 'utf8mb4'
}

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('mysql_fix.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def create_database_engine():
    """创建数据库引擎"""
    try:
        connection_url = (
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}"
            f"@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['database']}"
            f"?charset={MYSQL_CONFIG['charset']}&ssl_verify_cert=false&ssl_verify_identity=false"
        )

        engine = create_engine(
            connection_url,
            pool_size=5,
            max_overflow=10,
            pool_timeout=10,
            pool_recycle=3600,
            pool_pre_ping=True,
            echo=False,
            connect_args={
                'connect_timeout': 10,
                'read_timeout': 60,
                'write_timeout': 60,
                'charset': 'utf8mb4'
            }
        )

        # 测试连接
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.fetchone():
                logger.info("✅ 数据库连接成功")
                return engine
            else:
                raise Exception("数据库连接测试失败")

    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        raise

def check_and_fix_table_structure():
    """检查并修复表结构"""
    try:
        engine = create_database_engine()
        
        logger.info("🔍 检查large_buy_orders表结构...")
        with engine.connect() as conn:
            # 检查当前表结构
            result = conn.execute(text("DESCRIBE large_buy_orders"))
            columns = result.fetchall()
            
            negMarketValue_type = None
            for col in columns:
                if col[0] == 'negMarketValue':
                    negMarketValue_type = col[1]
                    logger.info(f"🎯 当前negMarketValue字段类型: {negMarketValue_type}")
                    break
            
            if negMarketValue_type and 'int(' in negMarketValue_type.lower():
                logger.warning("⚠️ negMarketValue字段为INT类型，需要修改为BIGINT")
                
                # 备份表结构
                logger.info("📋 备份表结构...")
                result = conn.execute(text("SHOW CREATE TABLE large_buy_orders"))
                create_table_sql = result.fetchone()[1]
                
                with open('large_buy_orders_backup_structure.sql', 'w', encoding='utf-8') as f:
                    f.write(f"-- 备份时间: {datetime.datetime.now()}\n")
                    f.write(f"-- 原始表结构:\n")
                    f.write(create_table_sql)
                
                logger.info("✅ 表结构已备份到 large_buy_orders_backup_structure.sql")
                
                # 修改字段类型
                logger.info("🔧 修改negMarketValue字段类型为BIGINT...")
                conn.execute(text("ALTER TABLE large_buy_orders MODIFY COLUMN negMarketValue BIGINT"))
                conn.commit()
                logger.info("✅ 字段类型修改成功")
                
                # 验证修改结果
                result = conn.execute(text("DESCRIBE large_buy_orders"))
                columns = result.fetchall()
                for col in columns:
                    if col[0] == 'negMarketValue':
                        logger.info(f"🎯 修改后negMarketValue字段类型: {col[1]}")
                        break
            else:
                logger.info("✅ negMarketValue字段类型已经是BIGINT或其他大数值类型")
        
        engine.dispose()
        return True
        
    except Exception as e:
        logger.error(f"❌ 修复表结构失败: {e}")
        return False

def enhanced_import_csv_to_mysql(engine, csv_file_path):
    """增强版CSV导入函数，包含完善的错误处理"""
    try:
        logger.info(f"📤 开始导入CSV文件到数据库: {csv_file_path}")

        # 读取CSV文件
        df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
        logger.info(f"📊 CSV文件读取成功，共 {len(df)} 行数据")

        if len(df) == 0:
            logger.warning("⚠️ CSV文件为空，跳过导入")
            return 0

        # 清理列名
        df_clean = df.copy()
        df_clean.columns = [
            col.replace('(', '_').replace(')', '_').replace('（', '_').replace('）', '_')
               .replace(' ', '_').replace('-', '_').replace('.', '_')
            for col in df_clean.columns
        ]

        # 添加时间戳
        df_clean['created_at'] = datetime.datetime.now()
        df_clean['updated_at'] = datetime.datetime.now()

        # 数据预处理：处理数值范围问题
        if 'negMarketValue' in df_clean.columns:
            logger.info("🔧 处理negMarketValue字段数值范围...")
            
            # 检查数值范围
            neg_market_values = df_clean['negMarketValue'].dropna()
            if len(neg_market_values) > 0:
                logger.info(f"📊 negMarketValue范围: {neg_market_values.min():,.0f} 到 {neg_market_values.max():,.0f}")
                
                # 确保数值在合理范围内（BIGINT范围）
                bigint_max = 9223372036854775807
                bigint_min = -9223372036854775808
                
                # 处理超出范围的值
                over_range = (neg_market_values > bigint_max) | (neg_market_values < bigint_min)
                if over_range.any():
                    logger.warning(f"⚠️ 发现 {over_range.sum()} 个超出BIGINT范围的值，将进行截断处理")
                    df_clean.loc[df_clean['negMarketValue'] > bigint_max, 'negMarketValue'] = bigint_max
                    df_clean.loc[df_clean['negMarketValue'] < bigint_min, 'negMarketValue'] = bigint_min

        # 先清空表数据
        logger.info("🗑️ 清空表数据...")
        with engine.connect() as conn:
            conn.execute(text("TRUNCATE TABLE large_buy_orders"))
            conn.commit()

        # 分批导入数据，增强错误处理
        batch_size = 10  # 减小批次大小以便更好地定位问题
        total_rows = len(df_clean)
        imported_rows = 0
        failed_rows = 0

        for i in range(0, total_rows, batch_size):
            batch_df = df_clean.iloc[i:i+batch_size]
            try:
                batch_df.to_sql(
                    'large_buy_orders',
                    engine,
                    if_exists='append',
                    index=False,
                    method='multi'
                )
                imported_rows += len(batch_df)
                logger.info(f"📊 已导入 {imported_rows}/{total_rows} 行数据 ({imported_rows/total_rows*100:.1f}%)")
            except Exception as batch_e:
                logger.warning(f"⚠️ 批次 {i//batch_size + 1} 导入失败，尝试逐行导入: {batch_e}")
                
                # 逐行导入
                for idx, row in batch_df.iterrows():
                    try:
                        row_df = pd.DataFrame([row])
                        row_df.to_sql(
                            'large_buy_orders',
                            engine,
                            if_exists='append',
                            index=False,
                            method='multi'
                        )
                        imported_rows += 1
                    except Exception as row_e:
                        failed_rows += 1
                        logger.error(f"❌ 第 {idx+1} 行导入失败: {row_e}")
                        logger.error(f"   问题数据: {dict(row)}")
                        continue

        # 验证导入结果
        with engine.connect() as conn:
            result = conn.execute(text("SELECT COUNT(*) as count FROM large_buy_orders"))
            count = result.fetchone()[0]
            logger.info(f"🔍 数据库中实际记录数: {count}")

        # 输出最终统计报告
        logger.info(f"✅ 数据导入完成！")
        logger.info(f"  📊 成功导入: {imported_rows} 行")
        logger.info(f"  ❌ 失败行数: {failed_rows} 行")
        logger.info(f"  📈 成功率: {imported_rows/(imported_rows+failed_rows)*100:.1f}%")

        return imported_rows

    except Exception as e:
        logger.error(f"❌ 数据导入失败: {e}")
        raise

def main():
    """主函数"""
    logger.info("🚀 开始MySQL数据范围错误修复程序")
    
    try:
        # 步骤1: 检查并修复表结构
        logger.info("=" * 60)
        logger.info("步骤1: 检查并修复表结构")
        logger.info("=" * 60)
        
        if not check_and_fix_table_structure():
            logger.error("❌ 表结构修复失败，程序终止")
            return
        
        logger.info("✅ 表结构检查/修复完成")
        
        # 步骤2: 提供使用说明
        logger.info("=" * 60)
        logger.info("步骤2: 使用说明")
        logger.info("=" * 60)
        
        logger.info("🔧 表结构已修复，现在可以重新运行原始导入程序")
        logger.info("📝 建议的使用方法:")
        logger.info("   1. 运行 tupo_dabimairu.py 程序")
        logger.info("   2. 程序将自动使用修复后的表结构")
        logger.info("   3. 如果仍有问题，请检查日志文件 mysql_fix.log")
        
        logger.info("🎉 修复程序执行完成！")
        
    except Exception as e:
        logger.error(f"💥 程序执行失败: {e}")

if __name__ == "__main__":
    main()
