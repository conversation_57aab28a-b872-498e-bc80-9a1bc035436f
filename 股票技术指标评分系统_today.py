#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票技术指标评分系统
基于验证的核心指标"i1_最高_收盘比例"和辅助指标的分层评分机制
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')


def score_stock_data(csv_file_path, output_file_path=None):
    """
    对股票CSV数据进行技术指标评分
    
    基于核心指标"i1_最高_收盘比例"和辅助指标的分层评分机制：
    - 基础分（核心指标）：满足条件得50分，否则0分
    - 加分项（辅助指标）：每满足一个条件累加相应分数
    
    Args:
        csv_file_path (str): 输入CSV文件路径
        output_file_path (str, optional): 输出文件路径，默认为None时在原文件名后加"_评分结果"
    
    Returns:
        pandas.DataFrame: 包含评分结果的DataFrame
    """
    
    print("=" * 60)
    print("股票技术指标评分系统")
    print("=" * 60)
    
    try:
        # 1. 加载数据
        print(f"正在加载数据文件: {csv_file_path}")
        if not os.path.exists(csv_file_path):
            raise FileNotFoundError(f"文件不存在: {csv_file_path}")
        
        df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
        print(f"数据加载成功，共 {len(df)} 条记录")
        
        # 2. 定义评分规则
        # 核心指标配置（基于85%阈值的最佳策略）
        core_indicator = 'i1_最高_收盘比例'
        core_threshold = 0.0583  # 85%阈值
        base_score = 50  # 基础分
        
        # 辅助指标配置（按重要性排序）
        auxiliary_indicators = {
            'i1_i_MA20_ratio': {'threshold': 1.0049, 'score': 15, 'name': 'MA20'},
            'i1_i_EMA10_ratio': {'threshold': 1.0136, 'score': 15, 'name': 'EMA10'},
            'i1_i_EMA5_ratio': {'threshold': 1.0268, 'score': 10, 'name': 'EMA5'},
            'i1_i_EMA20_ratio': {'threshold': 1.007, 'score': 10, 'name': 'EMA20'}
        }
        
        # 3. 检查必需的列
        required_columns = [core_indicator] + list(auxiliary_indicators.keys())
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"警告：缺少以下必需列: {missing_columns}")
            print("可用的列:", list(df.columns))
            raise ValueError(f"数据文件缺少必需的技术指标列: {missing_columns}")
        
        print(f"✓ 所有必需的技术指标列都存在")
        
        # 4. 数据预处理
        print("正在进行数据预处理...")
        df_work = df.copy()
        
        # 处理缺失值
        for col in required_columns:
            if df_work[col].isnull().any():
                null_count = df_work[col].isnull().sum()
                print(f"  处理 {col} 列的 {null_count} 个缺失值")
                # 使用中位数填充缺失值
                median_val = df_work[col].median()
                df_work[col].fillna(median_val, inplace=True)
        
        # 5. 计算评分
        print("正在计算技术指标评分...")
        
        # 初始化评分列
        df_work['综合评分'] = 0
        df_work['评分详情'] = ''
        
        # 统计变量
        core_passed = 0
        auxiliary_stats = {name: 0 for name in [info['name'] for info in auxiliary_indicators.values()]}
        
        # 逐行计算评分
        for idx, row in df_work.iterrows():
            score = 0
            details = []
            
            # 检查核心指标
            if pd.notna(row[core_indicator]) and row[core_indicator] >= core_threshold:
                score += base_score
                details.append("核心指标✓")
                core_passed += 1
                
                # 只有满足核心指标条件才检查辅助指标
                for indicator, config in auxiliary_indicators.items():
                    if pd.notna(row[indicator]) and row[indicator] >= config['threshold']:
                        score += config['score']
                        details.append(f"{config['name']}✓")
                        auxiliary_stats[config['name']] += 1
            else:
                details.append("核心指标✗")
            
            # 更新评分和详情
            df_work.at[idx, '综合评分'] = score
            df_work.at[idx, '评分详情'] = ', '.join(details) if details else '无满足条件'
        
        # 6. 按评分排序
        df_work = df_work.sort_values('综合评分', ascending=False).reset_index(drop=True)
        
        # 7. 生成评分统计
        print("\n" + "=" * 40)
        print("评分统计摘要")
        print("=" * 40)
        
        # 核心指标通过率
        core_pass_rate = (core_passed / len(df_work)) * 100
        print(f"核心指标通过数量: {core_passed} / {len(df_work)} ({core_pass_rate:.2f}%)")
        
        # 辅助指标统计
        print(f"\n辅助指标满足情况（仅统计核心指标通过的记录）:")
        for name, count in auxiliary_stats.items():
            if core_passed > 0:
                rate = (count / core_passed) * 100
                print(f"  {name}: {count} / {core_passed} ({rate:.2f}%)")
        
        # 分数段分布
        print(f"\n评分分布:")
        score_ranges = [
            (90, 100, "优秀"),
            (80, 89, "良好"), 
            (70, 79, "中等"),
            (50, 69, "及格"),
            (1, 49, "较差"),
            (0, 0, "不合格")
        ]
        
        for min_score, max_score, level in score_ranges:
            if min_score == max_score:
                count = len(df_work[df_work['综合评分'] == min_score])
            else:
                count = len(df_work[(df_work['综合评分'] >= min_score) & (df_work['综合评分'] <= max_score)])
            
            percentage = (count / len(df_work)) * 100
            print(f"  {level}({min_score}-{max_score}分): {count}条 ({percentage:.2f}%)")
        
        # 显示前10名
        print(f"\n前10名评分结果:")
        top_10 = df_work.head(10)
        display_columns = ['secID', 'secShortName', '综合评分', '评分详情'] if 'secID' in df_work.columns else ['综合评分', '评分详情']
        available_display_columns = [col for col in display_columns if col in df_work.columns]
        
        if available_display_columns:
            print(top_10[available_display_columns].to_string(index=False))
        else:
            print(top_10[['综合评分', '评分详情']].to_string(index=False))
        
        # 8. 保存结果
        if output_file_path is None:
            # 生成默认输出文件名
            base_name = os.path.splitext(csv_file_path)[0]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file_path = f"{base_name}_评分结果_{timestamp}.csv"
        
        # 只保留列“secID”、“secShortName”、“i1_反弹日期”、“综合评分”、“评分详情”、“i1_成交量比”、“i1_最高_收盘比例”
        df_work = df_work[['secID', 'secShortName', 'i1_反弹日期',  '综合评分', '评分详情', 'i1_成交量比', 'i1_最高_收盘比例']]
        df_work.to_csv(output_file_path, index=False, encoding='utf-8-sig')
        print(f"\n✓ 评分结果已保存到: {output_file_path}")
        
        return df_work
        
    except FileNotFoundError as e:
        print(f"❌ 文件错误: {e}")
        return None
    except ValueError as e:
        print(f"❌ 数据错误: {e}")
        return None
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None


def batch_score_files(file_list, output_dir=None):
    """
    批量处理多个CSV文件的评分
    
    Args:
        file_list (list): CSV文件路径列表
        output_dir (str, optional): 输出目录，默认为None时使用原文件目录
    
    Returns:
        dict: 处理结果字典，键为文件路径，值为处理状态
    """
    print("=" * 60)
    print("批量股票数据评分处理")
    print("=" * 60)
    
    results = {}
    
    for i, file_path in enumerate(file_list, 1):
        print(f"\n处理第 {i}/{len(file_list)} 个文件: {file_path}")
        
        try:
            # 生成输出文件路径
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
                base_name = os.path.basename(file_path)
                name_without_ext = os.path.splitext(base_name)[0]
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = os.path.join(output_dir, f"{name_without_ext}_评分结果_{timestamp}.csv")
            else:
                output_path = None
            
            # 执行评分
            result_df = score_stock_data(file_path, output_path)
            
            if result_df is not None:
                results[file_path] = "成功"
                print(f"✓ 文件 {file_path} 处理成功")
            else:
                results[file_path] = "失败"
                print(f"❌ 文件 {file_path} 处理失败")
                
        except Exception as e:
            results[file_path] = f"错误: {str(e)}"
            print(f"❌ 文件 {file_path} 处理出错: {e}")
    
    # 打印批量处理摘要
    print(f"\n" + "=" * 40)
    print("批量处理摘要")
    print("=" * 40)
    
    success_count = sum(1 for status in results.values() if status == "成功")
    total_count = len(file_list)
    
    print(f"总文件数: {total_count}")
    print(f"成功处理: {success_count}")
    print(f"失败数量: {total_count - success_count}")
    
    if total_count - success_count > 0:
        print(f"\n失败文件详情:")
        for file_path, status in results.items():
            if status != "成功":
                print(f"  {file_path}: {status}")
    
    return results


def main():
    """主函数 - 演示评分系统的使用"""
    # 示例：对单个文件进行评分
    csv_file_path = r"c:\Zhou\coding\gupiao\fenxi\15di_fantan_twiceVol_today.csv"
    
    try:
        print("开始股票技术指标评分...")
        result_df = score_stock_data(csv_file_path)
        
        if result_df is not None:
            print(f"\n✓ 评分完成！")
            print(f"共处理 {len(result_df)} 条记录")
            
            # 显示一些统计信息
            max_score = result_df['综合评分'].max()
            avg_score = result_df['综合评分'].mean()
            high_score_count = len(result_df[result_df['综合评分'] >= 80])
            
            print(f"最高分: {max_score}")
            print(f"平均分: {avg_score:.2f}")
            print(f"80分以上: {high_score_count} 条")
        else:
            print("评分处理失败")
    
    except Exception as e:
        print(f"程序执行出错：{str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 执行完成命令
        import subprocess
        try:
            subprocess.run(['echo', '搞完了'], shell=True, check=True)
        except:
            print("搞完了")


if __name__ == "__main__":
    main()
