import os
import sys
import logging
import pandas as pd
from datetime import datetime, timedelta
import requests
import paramiko
import numpy as np
import pymysql
from sqlalchemy import create_engine, text, MetaData, Table, Column, String, Integer, Numeric, Date, DateTime, Text, Boolean
from sqlalchemy.dialects.mysql import LONGTEXT
import warnings

# 忽略pandas警告
warnings.filterwarnings('ignore')

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': 'stock-31bk-stock-31bankuai.c.aivencloud.com',
    'port': 26991,
    'user': 'avnadmin',
    'password': 'AVNS_daq_tCJ6LP2VwbS_633',
    'database': 'defaultdb',
    'charset': 'utf8mb4'
}

# 数据库表名
TABLE_NAME = 'n_shape_candidates'

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('n_shape_analysis.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def create_database_engine():
    """创建数据库引擎"""
    try:
        connection_url = (
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}"
            f"@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['database']}"
            f"?charset={MYSQL_CONFIG['charset']}&ssl_verify_cert=false&ssl_verify_identity=false"
        )

        engine = create_engine(
            connection_url,
            pool_size=5,
            max_overflow=10,
            pool_timeout=10,
            pool_recycle=3600,
            pool_pre_ping=True,
            echo=False,
            connect_args={
                'connect_timeout': 10,
                'read_timeout': 60,
                'write_timeout': 60,
                'charset': 'utf8mb4'
            }
        )

        # 测试连接
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.fetchone():
                logger.info("✅ 数据库连接成功")
                return engine
            else:
                raise Exception("数据库连接测试失败")

    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        raise

def clear_table_data(engine):
    """清空n_shape_candidates表中的所有数据"""
    try:
        logger.info(f"🗑️ 正在清空表: {TABLE_NAME}")
        with engine.connect() as conn:
            conn.execute(text(f"TRUNCATE TABLE {TABLE_NAME}"))
            conn.commit()
            logger.info(f"✅ 表数据清空成功: {TABLE_NAME}")
    except Exception as e:
        logger.error(f"❌ 清空表数据失败: {e}")
        raise

def get_table_field_lengths(engine):
    """获取数据库表的字段长度限制"""
    try:
        logger.info(f"🔍 正在获取表字段长度限制: {TABLE_NAME}")
        field_lengths = {}

        with engine.connect() as conn:
            result = conn.execute(text(f"DESCRIBE {TABLE_NAME}"))
            columns = result.fetchall()

            for col in columns:
                field_name = col[0]
                field_type = col[1]

                # 解析字段长度
                max_length = None
                if 'varchar' in field_type.lower():
                    # 提取varchar长度
                    start = field_type.find('(')
                    end = field_type.find(')')
                    if start != -1 and end != -1:
                        max_length = int(field_type[start+1:end])
                elif 'text' in field_type.lower():
                    if 'longtext' in field_type.lower():
                        max_length = 4294967295  # LONGTEXT最大长度
                    elif 'mediumtext' in field_type.lower():
                        max_length = 16777215    # MEDIUMTEXT最大长度
                    else:
                        max_length = 65535       # TEXT最大长度

                if max_length:
                    field_lengths[field_name] = max_length

            logger.info(f"✅ 获取到 {len(field_lengths)} 个字段的长度限制")
            return field_lengths

    except Exception as e:
        logger.error(f"❌ 获取字段长度限制失败: {e}")
        return {}

def smart_truncate_text(text, max_length, field_name=""):
    """智能截断文本，支持去重和语义保持"""
    if not text or pd.isna(text):
        return text

    text = str(text).strip()
    if len(text) <= max_length:
        return text

    # 记录原始长度
    original_length = len(text)

    # 对于包含分隔符的字段，尝试去重
    separators = [' ', '，', ',', '；', ';', '、', '|']
    has_separator = any(sep in text for sep in separators)

    if has_separator:
        # 尝试去重处理
        for sep in separators:
            if sep in text:
                parts = [part.strip() for part in text.split(sep) if part.strip()]
                # 去重但保持顺序
                unique_parts = []
                seen = set()
                for part in parts:
                    if part not in seen:
                        unique_parts.append(part)
                        seen.add(part)

                deduplicated_text = sep.join(unique_parts)
                if len(deduplicated_text) <= max_length:
                    logger.debug(f"🔧 字段 {field_name} 去重成功: {original_length} -> {len(deduplicated_text)} 字符")
                    return deduplicated_text
                else:
                    text = deduplicated_text
                break

    # 如果去重后仍然超长，进行智能截断
    if len(text) > max_length:
        # 保留3个字符用于"..."后缀
        truncate_length = max_length - 3
        truncated_text = text[:truncate_length] + "..."
        logger.debug(f"✂️ 字段 {field_name} 截断: {original_length} -> {len(truncated_text)} 字符")
        return truncated_text

    return text

def import_csv_to_mysql(engine, csv_file_path):
    """将CSV文件导入到MySQL数据库"""
    try:
        logger.info(f"📤 开始导入CSV文件到数据库: {csv_file_path}")

        # 读取CSV文件
        if not os.path.exists(csv_file_path):
            raise FileNotFoundError(f"CSV文件不存在: {csv_file_path}")

        df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
        logger.info(f"📊 CSV文件读取成功，共 {len(df)} 行数据")

        if len(df) == 0:
            logger.warning("⚠️ CSV文件为空，跳过导入")
            return 0

        # 清理列名
        df_clean = df.copy()
        df_clean.columns = [
            col.replace('(', '_').replace(')', '_').replace('（', '_').replace('）', '_')
               .replace(' ', '_').replace('-', '_').replace('.', '_')
            for col in df_clean.columns
        ]

        # 添加时间戳
        df_clean['created_at'] = datetime.now()
        df_clean['updated_at'] = datetime.now()

        # 获取数据库字段长度限制
        field_lengths = get_table_field_lengths(engine)

        # 数据预处理：检查和截断超长字段
        truncation_stats = {
            'total_truncations': 0,
            'affected_rows': 0,
            'field_stats': {}
        }

        if field_lengths:
            logger.info("🔧 开始数据预处理，检查字段长度...")

            for col_name in df_clean.columns:
                if col_name in field_lengths:
                    max_length = field_lengths[col_name]
                    field_truncations = 0
                    affected_rows_for_field = 0

                    for idx in df_clean.index:
                        original_value = df_clean.at[idx, col_name]
                        if pd.notna(original_value):
                            original_str = str(original_value)
                            truncated_value = smart_truncate_text(original_str, max_length, col_name)

                            if len(str(truncated_value)) != len(original_str):
                                df_clean.at[idx, col_name] = truncated_value
                                field_truncations += 1
                                if affected_rows_for_field == 0:
                                    affected_rows_for_field = 1

                                logger.debug(f"✂️ 截断字段 {col_name} (行{idx+1}): {len(original_str)} -> {len(str(truncated_value))} 字符")

                    if field_truncations > 0:
                        truncation_stats['field_stats'][col_name] = {
                            'truncations': field_truncations,
                            'max_length': max_length
                        }
                        truncation_stats['total_truncations'] += field_truncations
                        truncation_stats['affected_rows'] += affected_rows_for_field
                        logger.info(f"📝 字段 {col_name}: 截断 {field_truncations} 次，最大长度限制 {max_length}")

            # 输出截断统计
            if truncation_stats['total_truncations'] > 0:
                logger.info(f"📊 数据预处理完成:")
                logger.info(f"  - 总截断次数: {truncation_stats['total_truncations']}")
                logger.info(f"  - 影响字段数: {len(truncation_stats['field_stats'])}")
                for field, stats in truncation_stats['field_stats'].items():
                    logger.info(f"  - {field}: {stats['truncations']} 次截断 (限制: {stats['max_length']} 字符)")
            else:
                logger.info("✅ 所有字段长度均符合要求，无需截断")

        # 先清空表数据
        clear_table_data(engine)

        # 批量导入数据
        batch_size = 50
        total_rows = len(df_clean)
        imported_rows = 0

        for i in range(0, total_rows, batch_size):
            batch_df = df_clean.iloc[i:i+batch_size]
            try:
                batch_df.to_sql(
                    TABLE_NAME,
                    engine,
                    if_exists='append',
                    index=False,
                    method='multi'
                )
                imported_rows += len(batch_df)
                logger.info(f"📊 已导入 {imported_rows}/{total_rows} 行数据 ({imported_rows/total_rows*100:.1f}%)")
            except Exception as e:
                logger.warning(f"⚠️ 批次导入失败，尝试逐行导入: {e}")
                # 如果批次导入失败，尝试逐行导入
                for _, row in batch_df.iterrows():
                    try:
                        row_df = pd.DataFrame([row])
                        row_df.to_sql(
                            TABLE_NAME,
                            engine,
                            if_exists='append',
                            index=False,
                            method='multi'
                        )
                        imported_rows += 1
                    except Exception as row_e:
                        logger.error(f"❌ 行导入失败: {row_e}")
                        continue
                logger.info(f"📊 已导入 {imported_rows}/{total_rows} 行数据 ({imported_rows/total_rows*100:.1f}%)")

        # 验证导入结果
        with engine.connect() as conn:
            result = conn.execute(text(f"SELECT COUNT(*) as count FROM {TABLE_NAME}"))
            count = result.fetchone()[0]
            logger.info(f"🔍 数据库中实际记录数: {count}")

        # 输出最终统计报告
        logger.info(f"✅ 数据导入完成！共导入 {imported_rows} 行数据")

        if truncation_stats['total_truncations'] > 0:
            logger.info(f"📊 字段截断统计报告:")
            logger.info(f"  🔢 总截断次数: {truncation_stats['total_truncations']}")
            logger.info(f"  📝 涉及字段数: {len(truncation_stats['field_stats'])}")
            logger.info(f"  📋 详细统计:")
            for field, stats in truncation_stats['field_stats'].items():
                logger.info(f"    - {field}: {stats['truncations']} 次截断 (字段限制: {stats['max_length']} 字符)")
            logger.info(f"  ✅ 所有数据已成功处理并导入，无数据丢失")
        else:
            logger.info(f"  ✅ 无字段长度问题，所有数据完整导入")

        return imported_rows

    except Exception as e:
        logger.error(f"❌ 数据导入失败: {e}")
        raise

# 定义目录路径
equd_dir = "d:\\Andy\\Data\\MktEqudGet\\"
limit_dir = "d:\\Andy\\Data\\MktLimitGet\\"
mkt_stock_factors_dir = r'd:\Andy\Data\MktStockFactorsOneDayGet'

# 程序开始
logger.info("🚀 开始N形待选股票分析程序")

# 定义日期范围
# start_date = "20240401"
# end_date = "20240430"
end_date = (datetime.today() - timedelta(days=3)).strftime("%Y%m%d")
start_date = (datetime.strptime(end_date, '%Y%m%d') - timedelta(days=22)).strftime("%Y%m%d")
logger.info(f"📅 分析日期范围: {start_date} 到 {end_date}")
print("Start date:", start_date)
print("End date:", end_date)
output_file = 'd:\\Andy\\gupiao\\N形待选_today.csv'  #' + end_date + '
logger.info(f"📁 输出文件路径: {output_file}")

# 获取所有交易日的文件名
all_trading_dates = sorted([f.split('.')[0] for f in os.listdir(equd_dir) if f.endswith('.csv')])
# 过滤日期范围内的交易日
trading_dates = [date for date in all_trading_dates if start_date <= date <= end_date]
logger.info(f"📊 找到 {len(trading_dates)} 个交易日")

# 读取并合并数据
logger.info("📖 开始读取和合并股票数据")
data = []
for date in trading_dates:
    equd_file = os.path.join(equd_dir, f"{date}.csv")
    limit_file = os.path.join(limit_dir, f"{date}.csv")
    mkt_stock_factors_file = os.path.join(mkt_stock_factors_dir, f"{date}.csv")

    if not os.path.exists(equd_file) or not os.path.exists(limit_file):
        continue  # 如果当天不是交易日，跳过

    equd_df = pd.read_csv(equd_file, encoding='gbk')
    limit_df = pd.read_csv(limit_file, encoding='gbk')
    mkt_stock_factors_df = pd.read_csv(mkt_stock_factors_file, encoding='gbk')

    merged_df = pd.merge(equd_df, limit_df, on=["secID", "secShortName"])
    merged_df = pd.merge(merged_df, mkt_stock_factors_df, on=["secID"])
    merged_df["date"] = date
    data.append(merged_df)

# 合并所有日期的数据
all_data = pd.concat(data)
logger.info(f"📈 合并完成，共 {len(all_data)} 条原始数据")

# 过滤不需要的数据
all_data = all_data[(all_data['ticker_x'] < 700000) &
        ((all_data['exchangeCD_x'] == "XSHE") | (all_data['exchangeCD_x'] == "XSHG")) &
        (~all_data['secShortName'].str.contains('B')) &
        (~all_data['secShortName'].str.contains('ST')) & (all_data['closePrice'] > 1)]
logger.info(f"🔍 过滤后剩余 {len(all_data)} 条数据")

# 按secID和日期排序
all_data = all_data.sort_values(by=["secID", "date"])

# 找到符合条件的记录
logger.info("🔎 开始分析N形待选股票")
results = []
total_groups = len(all_data['secID'].unique())
current_group = 0
logger.info(f"📊 需要分析 {total_groups} 只股票")

for secID, group in all_data.groupby("secID"):
    current_group += 1
    secShortName = group.iloc[0]["secShortName"]
    # print(f"Processing secID: {secID}, secShortName: {secShortName}, Progress: {current_group}/{total_groups}")
    group = group.reset_index(drop=True)
    i = 1  # 从第二天开始检查，因为需要检查前一天是否涨停
    while i < len(group) - 2:
        # 检查第1天的涨停，且前一天不是涨停
        chgPct_sum_last5days = group.loc[i-4:i, "chgPct"].sum()
        no_limit_down = all(group.loc[j, "limitDownPrice"] != group.loc[j, "closePrice"] for j in range(i+1, len(group)))
        # 确保涨停后不再出现涨停
        no_more_limit_up = all(group.loc[j, "limitUpPrice"] != group.loc[j, "closePrice"] for j in range(i+1, len(group)))
        turnoverVol_decrease = all(group.loc[j, "turnoverVol"] < group.loc[j-1, "turnoverVol"] for j in range(i+1, len(group)))
        turnoverVol_decrease_days = sum(group.loc[j, "turnoverVol"] < group.loc[j-1, "turnoverVol"] for j in range(i+1, len(group)))
        if (group.loc[i, "limitUpPrice"] == group.loc[i, "closePrice"] and
            ((i-1 >= 0 and group.loc[i-1, "limitUpPrice"] != group.loc[i-1, "closePrice"]) or
             (i-2 >= 0 and group.loc[i-2, "limitUpPrice"] != group.loc[i-2, "closePrice"])) and
            group.loc[i+1, "limitUpPrice"] != group.loc[i+1, "closePrice"] and
            group.loc[i+2, "limitUpPrice"] != group.loc[i+2, "closePrice"] and
            group.loc[i-4:i, "openPrice"].min() < group.loc[i+1, "lowestPrice"] and
            group.loc[i-4:i, "openPrice"].min() < group.loc[i+2, "lowestPrice"] and
            group.loc[i-2:i, "lowestPrice"].min() < group.loc[i+1:, "lowestPrice"].min() and
            (len(group.loc[i+3:]) == 0 or group.loc[i:i+2, "highestPrice"].max() >= group.loc[i+3:, "highestPrice"].max()) and
            ((group.loc[i, "secID"][:2] in ["00", "60"] and chgPct_sum_last5days < 0.4) or
             (group.loc[i, "secID"][:2] in ["30", "68"] and chgPct_sum_last5days < 0.6)) and no_limit_down and no_more_limit_up):
            # 检查接下来2天的下跌情况
            # if (group.loc[i+1, "chgPct"] <= 0) and (group.loc[i+2, "chgPct"] <= 0) and (group.loc[i+1, "KDJ_K"] > group.loc[i+1, "KDJ_D"]) and (group.loc[i+2, "KDJ_K"] > group.loc[i+2, "KDJ_D"]) and (group.loc[i+1, "MA5"] > group.loc[i+1, "MA10"]) and (group.loc[i+2, "MA5"] > group.loc[i+2, "MA10"]):
                chgPct_sum = group.loc[i+1:i+2, "chgPct"].sum()
                # last_down_closePrice = group.loc[i+1, "closePrice"]

                results.append({
                    "secID": secID,
                    "secShortName": group.loc[i, "secShortName"],
                    "涨停日期": group.loc[i, "date"],
                    # "up_days": 1,  # 1天涨停
                    # "下跌第一天": group.loc[i+1, "date"],
                    # "下跌第一天成交量": group.loc[i+1, "turnoverVol"],
                    # "下跌第二天": group.loc[i+2, "date"],
                    # "下跌第二天成交量": group.loc[i+2, "turnoverVol"],
                    # "下跌第三天成交量": group.loc[i+3, "turnoverVol"] if i+3 < len(group) else 0,
                    # "down_days": 2,  # 2天的下跌
                    # "下跌天数涨幅之和": chgPct_sum,  # 下跌天数的chgPct之和
                    # "上涨天数涨幅之和": chgPct_sum_last5days,
                    # "上涨前4天开盘价最低": group.loc[i-4:i, "openPrice"].min(),
                    # "上涨第一天开盘价": group.loc[i, "openPrice"],  # 开始上涨第一天的openPrice
                    # "下跌第一天最低价": group.loc[i+1, "lowestPrice"],
                    # "下跌第二天最低价": group.loc[i+2, "lowestPrice"],
                    "MA5": group.loc[len(group)-1, "MA5"],
                    # "EMA5": group.loc[i+2, "EMA5"],
                    "MA10": group.loc[len(group)-1, "MA10"],
                    # "EMA10": group.loc[i+2, "EMA10"],
                    "MA20": group.loc[len(group)-1, "MA20"],
                    # "前三天最最低价": group.loc[i-2:i, "lowestPrice"].min(),
                    "至今最最低价": group.loc[i+1:, "lowestPrice"].min(),
                    # "前高": group.loc[i:i+2, "highestPrice"].max(),
                    '缩量下跌天数': turnoverVol_decrease_days,
                    '持续缩量': "持续缩量" if turnoverVol_decrease else "否",
                    "最后一天收盘价": group["closePrice"].iloc[-1]
                })
                # 更新i的值，跳过已处理的部分
                i += 3
            # else:
            #     # 如果没有找到符合条件的下跌情况，i需要递增
            #     i += 1
        else:
            i += 1

# 转换结果为DataFrame
logger.info(f"📋 分析完成，找到 {len(results)} 个初步候选")
results_df = pd.DataFrame(results)
results_df = results_df.sort_values(by='涨停日期', ascending=False)
results_df = results_df.drop_duplicates(subset='secShortName', keep='last')
results_df = results_df[results_df['缩量下跌天数'] > 0]
logger.info(f"✅ 过滤后最终候选: {len(results_df)} 只股票")
# 添加新列，根据下跌第二天最低价与MA5、MA10、MA20的比较结果
results_df['最低价格位置'] = '5日线上'
results_df.loc[results_df['至今最最低价'] <= results_df['MA5'], '最低价格位置'] = '5日线以下'
results_df.loc[results_df['至今最最低价'] <= results_df['MA10'], '最低价格位置'] = '10日线以下'
results_df.loc[results_df['至今最最低价'] <= results_df['MA20'], '最低价格位置'] = '20日线以下'
results_df['收盘价格位置'] = '5日线上'
results_df.loc[results_df['最后一天收盘价'] <= results_df['MA5'], '收盘价格位置'] = '5日线以下'
results_df.loc[results_df['最后一天收盘价'] <= results_df['MA10'], '收盘价格位置'] = '10日线以下'
results_df.loc[results_df['最后一天收盘价'] <= results_df['MA20'], '收盘价格位置'] = '20日线以下'
results_df.drop(['至今最最低价','MA5','MA10','MA20'], axis=1, inplace=True)
# results_df['回拉幅度'] = round((results_df['最后一天收盘价']/results_df['至今最最低价']-1)*100, 2)
# 首先计算满足第一个条件的情况
# results_df['持续下跌缩量'] = np.where(results_df['下跌第一天成交量'] > results_df['下跌第二天成交量'], 2, 0)
# # 然后检查是否满足更严格的条件，如果满足则更新为3
# mask = (results_df['下跌第一天成交量'] > results_df['下跌第二天成交量']) & \
#        (results_df['下跌第二天成交量'] > results_df['下跌第三天成交量']) & \
#        (results_df['下跌第三天成交量'] != 0)
# results_df.loc[mask, '持续下跌缩量'] = 3

# 合并版块信息
zhangting_all = pd.read_csv(r'd:\Andy\gupiao\zhangting_all_unique.csv')
results_df = pd.merge(results_df, zhangting_all, how='left', on=['secID'])
stock_board_concept_name_ths_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_ths_merged.csv', encoding='utf-8-sig')
stock_board_concept_name_ths_merged.drop(['secShortName'], axis=1, inplace=True)
stock_board_concept_name_em_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_em_merged.csv', encoding='utf-8-sig')
stock_board_concept_name_em_merged['secID'] = stock_board_concept_name_em_merged['代码'].astype(str).str.zfill(6).apply(lambda x: x + '.XSHE' if x.startswith(('00', '30')) else (x + '.XSHG' if x.startswith(('60', '68')) else x))
stock_board_concept_name_em_merged.drop(['序号','代码','secShortName_x','最新价','涨跌幅', '涨跌额', '成交量', '成交额', '振幅', '最高', '最低', '今开', '昨收', '换手率', '市盈率-动态', '市净率'], axis=1, inplace=True)
results_df = pd.merge(results_df, stock_board_concept_name_ths_merged, how='left', on=['secID'])
results_df = pd.merge(results_df, stock_board_concept_name_em_merged, how='left', on=['secID'])

# 保存结果到CSV文件
results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
logger.info(f"✅ CSV文件保存成功: {output_file}")

# 导入数据到MySQL数据库
try:
    logger.info("🚀 开始导入数据到MySQL数据库")
    engine = create_database_engine()
    imported_rows = import_csv_to_mysql(engine, output_file)
    logger.info(f"🎉 数据库导入完成！共导入 {imported_rows} 行数据到 {TABLE_NAME} 表")
except Exception as e:
    logger.error(f"💥 数据库导入失败: {e}")
    logger.info("⚠️ CSV文件已保存，但数据库导入失败，请检查数据库连接")
finally:
    if 'engine' in locals():
        engine.dispose()
        logger.info("🔌 数据库连接已关闭")

# upload_file_to_ubuntu(output_file, remote_dir_path, ubuntu_server_ip, ubuntu_username, ubuntu_password)
results_df = results_df[['secID', 'secShortName']]
list1 = pd.read_csv(r"d:\Andy\gupiao\list_withrongzi.csv")
list1 = pd.concat([list1, results_df])
list1.to_csv(r"d:\Andy\gupiao\list_withrongzi.csv", index=False, encoding='utf-8-sig')
local_file_path = r"d:\Andy\gupiao\list_withrongzi.csv"

logger.info("🎉 N形待选股票分析程序执行完成！")
print("\n✅ N形待选股票分析和数据库导入完成！")