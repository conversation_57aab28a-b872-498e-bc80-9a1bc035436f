-- MySQL数据范围错误修复SQL脚本
-- 修复large_buy_orders表中negMarketValue字段的数据类型问题
-- 执行时间: 2025-01-15

-- 1. 备份当前表结构（可选）
-- CREATE TABLE large_buy_orders_backup AS SELECT * FROM large_buy_orders LIMIT 0;

-- 2. 查看当前表结构
DESCRIBE large_buy_orders;

-- 3. 修改negMarketValue字段类型为BIGINT（推荐方案）
-- BIGINT范围: -9,223,372,036,854,775,808 到 9,223,372,036,854,775,807
ALTER TABLE large_buy_orders MODIFY COLUMN negMarketValue BIGINT;

-- 4. 验证修改结果
DESCRIBE large_buy_orders;

-- 5. 可选：如果需要更高精度，可以使用DECIMAL类型
-- ALTER TABLE large_buy_orders MODIFY COLUMN negMarketValue DECIMAL(20,2);

-- 6. 可选：如果需要浮点数支持，可以使用DOUBLE类型
-- ALTER TABLE large_buy_orders MODIFY COLUMN negMarketValue DOUBLE;

-- 执行说明：
-- 1. 连接到MySQL数据库
-- 2. 选择正确的数据库: USE defaultdb;
-- 3. 执行上述ALTER TABLE语句
-- 4. 验证修改是否成功
-- 5. 重新运行Python导入程序
