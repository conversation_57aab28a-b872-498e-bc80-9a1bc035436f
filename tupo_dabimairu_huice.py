from datetime import datetime, timedelta, date
import time
import os
import pandas as pd
from collections import deque, defaultdict
import requests
import warnings

warnings.filterwarnings('ignore')

save_path = r'd:\Andy\gupiao\Data\Minutes_dabi'

# 创建日期范围列表
start_date = datetime.strptime('20250430', '%Y%m%d')
end_date = datetime.strptime('20250529', '%Y%m%d')
date_range = [(start_date + timedelta(days=i)).strftime("%Y%m%d") 
              for i in range((end_date - start_date).days + 1)]

# 遍历日期范围
for predictDate in date_range:
    print(f"正在处理日期: {predictDate}")
    dir_path = save_path + '/' + predictDate + '/'
    dir_path_minites = r'd:\Andy\gupiao\Data\Minutes' + '/' + predictDate + '/'
    
    # 检查目录是否存在
    if not os.path.exists(dir_path):
        print(f"目录 {dir_path} 不存在，跳过此日期")
        continue
    
    Nxing_date = (datetime.strptime(predictDate, '%Y%m%d') - timedelta(days=1)).strftime("%Y%m%d")
    
    # 检查N形待选文件是否存在，如果不存在则跳过当前日期
    n_shape_file_exists = False
    temp_Nxing_date = Nxing_date
    for _ in range(1):  # 尝试最多1天前的文件
        if os.path.exists(r'N形待选_' + temp_Nxing_date + '.csv'):
            Nxing_date = temp_Nxing_date
            n_shape_file_exists = True
            break
        temp_Nxing_date = (datetime.strptime(temp_Nxing_date, '%Y%m%d') - timedelta(days=1)).strftime("%Y%m%d")
    
    if not n_shape_file_exists:
        print(f"找不到N形待选文件，跳过日期 {predictDate}")
        continue
    
    # 重置数据结构，为每个日期创建新的数据存储
    stock_time_slots = defaultdict(set)
    stock_time_data = defaultdict(dict)
    reminder_records = []
    
    def count_csv_files(path):
        """统计指定目录下的CSV文件数量"""
        return sum(1 for filename in os.listdir(path) if filename.lower().endswith('.csv'))

    def get_time_slot(time_str):
        """将时间字符串转换为分钟时间槽"""
        try:
            hour, minute, second = map(int, time_str.split(':'))
            total_minutes = hour * 60 + minute
            return total_minutes // 1
        except (AttributeError, ValueError, TypeError):
            # 如果time_str不是字符串或格式不正确，返回None
            return None

    file_index = 1
    # k等于120或者count_csv_files(dir_path)两者中的最小值
    try:
        k = min(120, count_csv_files(dir_path))
    except (FileNotFoundError, OSError):
        print(f"无法访问目录 {dir_path}，跳过此日期")
        continue

    while k > 0:
        print(f"处理文件 {file_index}/{k}")
        file_path = f'{dir_path}/{predictDate}_{file_index:03d}.csv'
        
        if os.path.exists(file_path):
            try:
                stock_changes_em_df = pd.read_csv(file_path)
                N_shape_pool = pd.read_csv(r'N形待选_' + Nxing_date + '.csv')
                
                # 合并N形待选池
                stock_changes_em_df_dabi = stock_changes_em_df.merge(N_shape_pool, on='secID', how='left')
                stock_changes_em_df_dabi.dropna(subset=['secShortName'], inplace=True)
                
                # 筛选符合条件的股票
                daemairu = stock_changes_em_df_dabi[(stock_changes_em_df_dabi['buyin'] > 800)]
                daemairu = daemairu[(daemairu['chgPct'] > -5) & (daemairu['chgPct'] < 8)]
                # daemairu = daemairu[daemairu['收盘价格位置'].str.startswith('5日')]
                daemairu = daemairu[daemairu['时间'] >= '09:31:00']
                # 新加一列，是同一个secID列'buyin'的值之和
                daemairu['buyin_value'] = daemairu.groupby('secID')['buyin'].transform('sum')
                # 只留下'buyin_value'大于2000的行
                # daemairu = daemairu[daemairu['buyin_value'] > 2000]
                
                file_index_minutes = file_index + 1
                file_path_minutes = f'{dir_path_minites}/{predictDate}_{file_index_minutes:03d}.csv'
                # 当满足条件file_index_minutes从当前值一直到120，
                while file_index_minutes <= 120:
                    if os.path.exists(file_path_minutes):
                        try:
                            stock_changes_em_df_minutes = pd.read_csv(file_path_minutes)
                            # 只留下stock_changes_em_df_minutes的'secID'列的值出现在daemairu的'secID'列的行
                            stock_changes_em_df_minutes_filtered = stock_changes_em_df_minutes[stock_changes_em_df_minutes['secID'].isin(daemairu['secID'])]
                            
                            # 找出涨跌幅小于4的股票，将其添加到提醒列表中
                            # reminder_stocks = stock_changes_em_df_minutes_filtered[stock_changes_em_df_minutes_filtered['涨跌幅'] < 0]
                            reminder_stocks = stock_changes_em_df_minutes_filtered[stock_changes_em_df_minutes_filtered['最新价'] <= stock_changes_em_df_minutes_filtered['最低']]
                            for _, row in reminder_stocks.iterrows():
                                reminder_info = row.to_dict()
                                reminder_info['提醒时间'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                                reminder_info['原始文件'] = os.path.basename(file_path_minutes)
                                reminder_records.append(reminder_info)
                            
                            # 继续原有的筛选逻辑
                            stock_changes_em_df_minutes = stock_changes_em_df_minutes_filtered[stock_changes_em_df_minutes_filtered['涨跌幅'] < 0]
                            # 只留下daemairu的'secID'列的值出现在stock_changes_em_df_minutes的'secID'列的行
                            daemairu = daemairu[daemairu['secID'].isin(stock_changes_em_df_minutes['secID'])]
                        except Exception as e:
                            print(f"处理文件 {file_path_minutes} 时出错: {e}")
                    file_index_minutes += 1
                    file_path_minutes = f'{dir_path_minites}/{predictDate}_{file_index_minutes:03d}.csv'
                    
                # 更新每只股票的出现记录和数据
                for _, row in daemairu.iterrows():
                    secID = row['secID']
                    time_str = row['时间']
                    time_slot = get_time_slot(time_str)
                    
                    # 只有当time_slot有效时才记录
                    if time_slot is not None:
                        # 记录该股票在这个时间槽的出现
                        stock_time_slots[secID].add(time_slot)
                        
                        # 使用时间作为key来存储数据，避免重复
                        stock_time_data[secID][str(time_str)] = row.to_dict()
            except Exception as e:
                print(f"处理文件 {file_path} 时出错: {e}")
        
        k -= 1
        file_index += 1

    # 创建结果DataFrame
    all_records = []
    for secID, time_slots in stock_time_slots.items():
        # 获取该股票的所有时间点记录
        records = list(stock_time_data[secID].values())  # 使用values()来获取唯一的记录
        # 为每条记录添加出现次数
        for record in records:
            record['出现次数'] = len(time_slots)
            all_records.append(record)

    # 转换为DataFrame并按时间排序
    filtered_stocks = pd.DataFrame(all_records)
    if not filtered_stocks.empty:
        # 确保按secID和时间排序，并去除完全重复的行
        filtered_stocks = filtered_stocks.sort_values(['secID', '时间']).drop_duplicates()

    # 保存符合条件的股票到founded_N型.csv
    if len(filtered_stocks) > 0:
        # 保存结果
        filtered_stocks.to_csv(f'founded_N型_' + predictDate + '.csv', index=False, encoding='utf-8-sig')
        
        # 打印统计信息
        unique_stocks = filtered_stocks['secID'].nunique()
        total_records = len(filtered_stocks)
        print(f"处理完成，日期 {predictDate}，共找到{unique_stocks}支符合条件的股票，总计{total_records}条记录")

    # 保存提醒信息到founded_N型_日期_提醒.csv
    if len(reminder_records) > 0:
        reminder_df = pd.DataFrame(reminder_records)
        
        # 检查列名并安全排序
        sort_columns = []
        if 'secID' in reminder_df.columns:
            sort_columns.append('secID')
        if '时间' in reminder_df.columns:
            sort_columns.append('时间')
        elif '提醒时间' in reminder_df.columns:
            sort_columns.append('提醒时间')
        
        # 只有在有排序列的情况下才进行排序
        if sort_columns:
            reminder_df = reminder_df.sort_values(sort_columns).drop_duplicates()
        else:
            reminder_df = reminder_df.drop_duplicates()

        # 列"secID"相同的行只留下最早的   
        reminder_df = reminder_df.sort_values('原始文件').drop_duplicates('secID', keep='first')
        reminder_df.to_csv(f'founded_N型_' + predictDate + '_提醒.csv', index=False, encoding='utf-8-sig')
        print(f"处理完成，日期 {predictDate}，共生成{len(reminder_df)}条提醒记录，涉及{reminder_df['secID'].nunique()}支股票")
    
    print(f"日期 {predictDate} 处理完毕\n" + "-"*50)

print("所有日期处理完成！")
