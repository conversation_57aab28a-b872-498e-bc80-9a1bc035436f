#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MySQL存储功能
"""

import pandas as pd
import datetime
import logging
import sys
from sqlalchemy import create_engine, text

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': 'stock-31bk-stock-31bankuai.c.aivencloud.com',
    'port': 26991,
    'user': 'avnadmin',
    'password': 'AVNS_daq_tCJ6LP2VwbS_633',
    'database': 'defaultdb',
    'charset': 'utf8mb4'
}

def create_database_engine():
    """创建数据库引擎"""
    try:
        connection_url = (
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}"
            f"@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['database']}"
            f"?charset={MYSQL_CONFIG['charset']}&ssl_verify_cert=false&ssl_verify_identity=false"
        )
        
        engine = create_engine(
            connection_url,
            pool_size=5,
            max_overflow=10,
            pool_timeout=10,
            pool_recycle=3600,
            pool_pre_ping=True,
            echo=False,
            connect_args={
                'connect_timeout': 10,
                'read_timeout': 60,
                'write_timeout': 60,
                'charset': 'utf8mb4'
            }
        )
        
        # 测试连接
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.fetchone():
                logger.info("✅ 数据库连接成功")
                return engine
            else:
                raise Exception("数据库连接测试失败")
                
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        return None

def test_database_connection():
    """测试数据库连接"""
    print("=== 测试数据库连接 ===")
    
    engine = create_database_engine()
    if engine is None:
        print("❌ 数据库连接失败")
        return False
    
    try:
        # 测试查询
        with engine.connect() as conn:
            result = conn.execute(text("SHOW TABLES"))
            tables = result.fetchall()
            print(f"✅ 数据库连接成功，当前数据库中有 {len(tables)} 个表")
            
            # 显示现有表
            if tables:
                print("现有表:")
                for table in tables:
                    print(f"  - {table[0]}")
            
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
        return False
    finally:
        engine.dispose()

def test_table_creation():
    """测试表创建功能"""
    print("\n=== 测试表创建功能 ===")
    
    engine = create_database_engine()
    if engine is None:
        print("❌ 数据库连接失败")
        return False
    
    try:
        # 导入必要的函数
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from zhuazhangting import create_zhangting_table, create_concept_stats_table
        
        # 测试创建涨停股表
        print("创建涨停股数据表...")
        create_zhangting_table(engine)
        
        # 测试创建题材统计表
        print("创建题材统计表...")
        create_concept_stats_table(engine, 'zhangting_concept_stats')
        create_concept_stats_table(engine, 'zhangting_concept_stats_0925')
        
        # 验证表是否创建成功
        with engine.connect() as conn:
            result = conn.execute(text("SHOW TABLES LIKE 'zhangting%'"))
            tables = result.fetchall()
            print(f"✅ 成功创建 {len(tables)} 个表:")
            for table in tables:
                print(f"  - {table[0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 表创建失败: {e}")
        return False
    finally:
        engine.dispose()

def test_data_import():
    """测试数据导入功能"""
    print("\n=== 测试数据导入功能 ===")
    
    engine = create_database_engine()
    if engine is None:
        print("❌ 数据库连接失败")
        return False
    
    try:
        # 导入必要的函数
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from zhuazhangting import import_zhangting_data_to_mysql, import_concept_stats_to_mysql
        
        # 创建测试数据
        print("创建测试数据...")
        
        # 涨停股测试数据
        zhangting_test_data = pd.DataFrame({
            'secID': ['000001.XSHE', '000002.XSHE'],
            '名称': ['平安银行', '万科A'],
            '首次': ['09:30', '09:31'],
            '最后': ['09:30', '09:31'],
            '价格': [10.50, 25.80],
            '连板': [1, 2],
            '涨幅%': [10.0, 10.0],
            '题材': ['大金融 银行', '房地产 地产'],
            '涨停原因': ['业绩增长', '政策利好'],
            '题材（G）': ['大金融 银行概念 金融服务', '房地产 地产开发 住宅地产']
        })
        
        # 题材统计测试数据
        concept_test_data = pd.DataFrame({
            '题材': ['大金融', '房地产', '医药'],
            '涨停股出现次数': [5, 3, 2],
            '涨停股出现次数_上次': [4, 2, 1],
            '比上次次数升降': [1, 1, 1],
            '涨停股出现次数_初始': [3, 1, 1],
            '比初始次数升降': [2, 2, 1]
        })
        
        # 测试涨停股数据导入
        print("测试涨停股数据导入...")
        result1 = import_zhangting_data_to_mysql(engine, zhangting_test_data)
        print(f"✅ 涨停股数据导入结果: {result1} 行")
        
        # 测试题材统计数据导入
        print("测试题材统计数据导入...")
        result2 = import_concept_stats_to_mysql(engine, concept_test_data, 'zhangting_concept_stats')
        print(f"✅ 题材统计数据导入结果: {result2} 行")
        
        # 验证数据是否导入成功
        with engine.connect() as conn:
            # 检查涨停股表
            result = conn.execute(text("SELECT COUNT(*) FROM zhangting"))
            count1 = result.fetchone()[0]
            print(f"✅ 涨停股表中有 {count1} 行数据")
            
            # 检查题材统计表
            result = conn.execute(text("SELECT COUNT(*) FROM zhangting_concept_stats"))
            count2 = result.fetchone()[0]
            print(f"✅ 题材统计表中有 {count2} 行数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据导入测试失败: {e}")
        return False
    finally:
        engine.dispose()

def main():
    """主测试函数"""
    print("🚀 开始MySQL存储功能测试")
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 数据库连接
    if test_database_connection():
        success_count += 1
    
    # 测试2: 表创建
    if test_table_creation():
        success_count += 1
    
    # 测试3: 数据导入
    if test_data_import():
        success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！MySQL存储功能正常工作。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置和网络连接。")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
