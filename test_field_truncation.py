#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试字段截断功能
"""

import os
import sys
import logging
import pandas as pd
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

def test_smart_truncate_text():
    """测试智能文本截断函数"""
    # 导入函数
    from sqlalchemy import create_engine, text
    
    # MySQL数据库配置
    MYSQL_CONFIG = {
        'host': 'stock-31bk-stock-31bankuai.c.aivencloud.com',
        'port': 26991,
        'user': 'avnadmin',
        'password': 'AVNS_daq_tCJ6LP2VwbS_633',
        'database': 'defaultdb',
        'charset': 'utf8mb4'
    }
    
    def smart_truncate_text(text, max_length, field_name=""):
        """智能截断文本，支持去重和语义保持"""
        if not text or pd.isna(text):
            return text
        
        text = str(text).strip()
        if len(text) <= max_length:
            return text
        
        # 记录原始长度
        original_length = len(text)
        
        # 对于包含分隔符的字段，尝试去重
        separators = [' ', '，', ',', '；', ';', '、', '|']
        has_separator = any(sep in text for sep in separators)
        
        if has_separator:
            # 尝试去重处理
            for sep in separators:
                if sep in text:
                    parts = [part.strip() for part in text.split(sep) if part.strip()]
                    # 去重但保持顺序
                    unique_parts = []
                    seen = set()
                    for part in parts:
                        if part not in seen:
                            unique_parts.append(part)
                            seen.add(part)
                    
                    deduplicated_text = sep.join(unique_parts)
                    if len(deduplicated_text) <= max_length:
                        logger.info(f"🔧 字段 {field_name} 去重成功: {original_length} -> {len(deduplicated_text)} 字符")
                        return deduplicated_text
                    else:
                        text = deduplicated_text
                    break
        
        # 如果去重后仍然超长，进行智能截断
        if len(text) > max_length:
            # 保留3个字符用于"..."后缀
            truncate_length = max_length - 3
            truncated_text = text[:truncate_length] + "..."
            logger.info(f"✂️ 字段 {field_name} 截断: {original_length} -> {len(truncated_text)} 字符")
            return truncated_text
        
        return text
    
    # 测试用例
    test_cases = [
        {
            'text': '新能源汽车 新能源汽车 锂电池 锂电池 充电桩 新能源汽车 储能',
            'max_length': 50,
            'field_name': '题材汇总',
            'expected_behavior': '去重'
        },
        {
            'text': '这是一个非常长的文本内容，用来测试截断功能是否正常工作，应该会被截断并添加省略号',
            'max_length': 30,
            'field_name': '测试字段',
            'expected_behavior': '截断'
        },
        {
            'text': '短文本',
            'max_length': 50,
            'field_name': '正常字段',
            'expected_behavior': '不变'
        }
    ]
    
    logger.info("🧪 开始测试智能文本截断功能")
    
    for i, case in enumerate(test_cases, 1):
        logger.info(f"\n📝 测试用例 {i}: {case['expected_behavior']}")
        logger.info(f"原文本: {case['text']}")
        logger.info(f"最大长度: {case['max_length']}")
        
        result = smart_truncate_text(case['text'], case['max_length'], case['field_name'])
        
        logger.info(f"结果: {result}")
        logger.info(f"结果长度: {len(result)}")
        logger.info(f"是否符合长度要求: {'✅' if len(result) <= case['max_length'] else '❌'}")

def test_database_connection():
    """测试数据库连接和字段长度获取"""
    try:
        from sqlalchemy import create_engine, text
        
        # MySQL数据库配置
        MYSQL_CONFIG = {
            'host': 'stock-31bk-stock-31bankuai.c.aivencloud.com',
            'port': 26991,
            'user': 'avnadmin',
            'password': 'AVNS_daq_tCJ6LP2VwbS_633',
            'database': 'defaultdb',
            'charset': 'utf8mb4'
        }
        
        connection_url = (
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}"
            f"@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['database']}"
            f"?charset={MYSQL_CONFIG['charset']}&ssl_verify_cert=false&ssl_verify_identity=false"
        )
        
        engine = create_engine(connection_url, echo=False)
        
        with engine.connect() as conn:
            # 测试连接
            result = conn.execute(text("SELECT 1"))
            if result.fetchone():
                logger.info("✅ 数据库连接成功")
                
                # 获取表结构
                TABLE_NAME = 'n_shape_candidates'
                result = conn.execute(text(f"DESCRIBE {TABLE_NAME}"))
                columns = result.fetchall()
                
                logger.info(f"📋 表 {TABLE_NAME} 的字段信息:")
                varchar_fields = []
                
                for col in columns:
                    field_name = col[0]
                    field_type = col[1]
                    
                    if 'varchar' in field_type.lower():
                        start = field_type.find('(')
                        end = field_type.find(')')
                        if start != -1 and end != -1:
                            max_length = int(field_type[start+1:end])
                            varchar_fields.append((field_name, max_length))
                            logger.info(f"  📝 {field_name}: {field_type} (最大长度: {max_length})")
                
                logger.info(f"✅ 找到 {len(varchar_fields)} 个VARCHAR字段")
                return True
            else:
                logger.error("❌ 数据库连接测试失败")
                return False
                
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        return False
    finally:
        if 'engine' in locals():
            engine.dispose()

if __name__ == "__main__":
    logger.info("🚀 开始字段截断功能测试")
    
    # 测试智能截断函数
    test_smart_truncate_text()
    
    print("\n" + "="*50)
    
    # 测试数据库连接
    if test_database_connection():
        logger.info("✅ 数据库连接测试通过")
    else:
        logger.error("❌ 数据库连接测试失败")
    
    print("\n✅ 字段截断功能测试完成！")
