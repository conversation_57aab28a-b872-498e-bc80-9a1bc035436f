#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试数据库连接和表结构
"""

import sys
import logging
import pandas as pd
from datetime import datetime
from sqlalchemy import create_engine, text

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': 'stock-31bk-stock-31bankuai.c.aivencloud.com',
    'port': 26991,
    'user': 'avnadmin',
    'password': 'AVNS_daq_tCJ6LP2VwbS_633',
    'database': 'defaultdb',
    'charset': 'utf8mb4'
}

TABLE_NAME = 'n_shape_candidates'

def test_database_connection():
    """测试数据库连接"""
    try:
        connection_url = (
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}"
            f"@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['database']}"
            f"?charset={MYSQL_CONFIG['charset']}&ssl_verify_cert=false&ssl_verify_identity=false"
        )
        
        engine = create_engine(connection_url, echo=False)
        
        # 测试连接
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.fetchone():
                logger.info("✅ 数据库连接成功")
                
                # 检查表是否存在
                result = conn.execute(text(f"SHOW TABLES LIKE '{TABLE_NAME}'"))
                if result.fetchone():
                    logger.info(f"✅ 表 {TABLE_NAME} 存在")
                    
                    # 查看表结构
                    result = conn.execute(text(f"DESCRIBE {TABLE_NAME}"))
                    columns = result.fetchall()
                    logger.info(f"📋 表结构:")
                    for col in columns:
                        logger.info(f"  - {col[0]}: {col[1]}")
                    
                    # 查看记录数
                    result = conn.execute(text(f"SELECT COUNT(*) FROM {TABLE_NAME}"))
                    count = result.fetchone()[0]
                    logger.info(f"📊 当前记录数: {count}")
                else:
                    logger.warning(f"⚠️ 表 {TABLE_NAME} 不存在")
                
                return True
            else:
                logger.error("❌ 数据库连接测试失败")
                return False
                
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        return False
    finally:
        if 'engine' in locals():
            engine.dispose()

if __name__ == "__main__":
    logger.info("🚀 开始测试数据库连接")
    success = test_database_connection()
    if success:
        print("\n✅ 数据库连接测试成功！")
    else:
        print("\n❌ 数据库连接测试失败！")
