from playwright.sync_api import sync_playwright
from bs4 import BeautifulSoup
import datetime
import pandas as pd
import csv
import os
import time
from collections import Counter
import akshare as ak
import paramiko

def upload_file_to_ubuntu(local_file_path, remote_dir_path, server_ip, username, password):
    try:
        # 建立SSH传输连接
        transport = paramiko.Transport((server_ip, 22))
        transport.connect(username=username, password=password)

        # 创建SFTP客户端
        sftp = paramiko.SFTPClient.from_transport(transport)

        # 检查远程目录是否存在,不存在则创建
        try:
            sftp.stat(remote_dir_path)
        except IOError:
            sftp.mkdir(remote_dir_path)

        # 获取本地文件名
        local_filename = os.path.basename(local_file_path)
        
        # 构建远程文件完整路径
        remote_file_path = os.path.join(remote_dir_path, local_filename)

        # 上传文件
        sftp.put(local_file_path, remote_file_path)
        print(f"文件 '{local_file_path}' 已成功上传到远程服务器 '{remote_file_path}'")

        # 关闭连接
        sftp.close()
        transport.close()

    except Exception as e:
        print(f"上传文件时出错: {e}")

remote_dir_path = "/var/www/html/files/"
ubuntu_server_ip = "*************"
ubuntu_username = "user1/root"
ubuntu_password = "@uSJVBqSCP2E"

current_time = datetime.datetime.now().time()
while current_time < datetime.time(11, 30) or (current_time > datetime.time(13, 00) and current_time < datetime.time(15, 00)):

    # 访问新的网页
    url = "http://www.wuylh.com/zthelper/ztindex.jsp"

    try:
        # 使用Playwright启动Edge浏览器
        with sync_playwright() as p:
            # 启动Edge浏览器（无头模式）
            browser = p.chromium.launch(
                headless=True,
                args=[
                    "--disable-gpu",
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-extensions",
                    "--disable-background-networking",
                    "--disable-sync",
                    "--disable-translate",
                    "--ignore-ssl-errors-on-localhost",
                    "--ignore-certificate-errors"
                ]
            )

            # 创建新的页面
            page = browser.new_page()

            # 访问网页
            page.goto(url)
            time.sleep(6)

            # 等待表格加载完成
            page.wait_for_selector("#limituptable", timeout=20000)

            # 等待加载提示消失
            page.wait_for_selector("#loading", state="hidden", timeout=20000)

            # 额外等待一些时间，确保数据完全加载
            time.sleep(5)

            # 获取页面源代码
            page_source = page.content()

            # 关闭浏览器
            browser.close()

        # 使用BeautifulSoup解析页面源代码
        soup = BeautifulSoup(page_source, 'html.parser')
    
        # 提取表格数据
        table = soup.find('table', id='limituptable')
        if table:
            rows = table.find_all('tr')
    
            # 准备CSV文件
            filename = r'D:\Andy\gupiao\zhangting.csv'
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入表头
                headers = [th.text.strip() for th in rows[0].find_all('th')]
                #headers = headers[1:]  # 移除第一个元素（假设它是 "No." 列）
                writer.writerow(headers)
                # print(headers)
                
                # 写入数据行
                for row in rows[1:]:
                    data = [td.text.strip() for td in row.find_all('td')]
                    if data:  # 只有当数据行非空时才处理
                        data = data[:-1]  # 移除第一个元素（与表头处理保持一致）
                        writer.writerow(data)
            # 读取CSV文件
            df = pd.read_csv(filename, encoding='utf-8-sig')
            if len(df) > 0:  
                
                # 要删除的列名列表
                columns_to_remove = ['No.', '资讯', '金额(亿)', '一年', '流通市值', '机构', '今竞%', '今竞', '隔夜单', '次竞%', '次竞', '次竞量', '今竞封单', '次竞封单', '次收', '次高']
                
                # 删除指定的列
                df = df.drop(columns=columns_to_remove, errors='ignore')
                # 只保留"连板"大于0的行
                df = df[df['连板'].astype(int) > 0]
                
                # 重置索引
                df = df.reset_index(drop=True)
                # 将"名 称"列按空格拆分为"secID"和"名称"两列
                df[['secID', '名称']] = df['名 称'].str.split(expand=True)
                # 根据secID的开头添加后缀
                df['secID'] = df['secID'].apply(lambda x: x + '.XSHE' if x.startswith(('00', '30')) else (x + '.XSHG' if x.startswith(('60', '68')) else x))
                # 删除原来的"名 称"列
                df = df.drop(columns=['名 称'])
                
                #####
                MktLimitGet = pd.read_csv(r'd:\Andy\gupiao\MktLimitGet.csv', encoding='GBK')
                MktLimitGet = MktLimitGet[['secID', 'limitUpPrice']]
                dfo_zhangting = df[['secID', '名称', '价格']]
                dfo_zhangting=pd.merge(dfo_zhangting, MktLimitGet, how='left', on=['secID'])
                dfo_zhangting = dfo_zhangting[dfo_zhangting['价格'] == dfo_zhangting['limitUpPrice']]
                #####
                
                if len(dfo_zhangting) > 0:                
                    
                    # 调整列的顺序，将"代码"和"名称"放在最前面
                    columns = df.columns.tolist()
                    columns = ['secID', '名称'] + [col for col in columns if col not in ['secID', '名称']]
                    df = df[columns]
                    # 处理'名称'列
                    df.loc[(df['名称'].str.len() > 4) & (df['名称'].str[-1] == '创'), '名称'] = df['名称'].str[:-1]
                    df = df.rename(columns={col: '涨停原因' for col in df.columns if col.startswith('涨停原因')})

                    # 确保'题材'列为字符串类型，避免数据类型兼容性问题
                    if '题材' in df.columns:
                        df['题材'] = df['题材'].astype('object')

                    # 如果'题材'列为空，则从'涨停原因'列提取内容
                    if '涨停原因' in df.columns:
                        mask = df['题材'].isna()
                        if mask.any():
                            extracted_values = df.loc[mask, '涨停原因'].str.split(n=1).str[0]
                            df.loc[mask, '题材'] = extracted_values
            
                    ####以防万一
                    df_all = pd.read_csv(r'd:\Andy\gupiao\zhangting_all_unique.csv')
                    # 确保df_all的'题材'列也是字符串类型
                    if '题材' in df_all.columns:
                        df_all['题材'] = df_all['题材'].astype('object')

                    # 如果df中'题材'列为空,从df_all中相同secID的'题材'列获取内容
                    mask_gengduo = df['题材'] == '更多...'
                    if mask_gengduo.any():
                        mapped_values = df.loc[mask_gengduo, 'secID'].map(df_all.set_index('secID')['题材'])
                        df.loc[mask_gengduo, '题材'] = mapped_values
                    ####        
                    # 将处理后的数据保存回CSV文件
                    df.to_csv(filename, index=False, encoding='utf-8-sig')
                
                else:
                    dfo = ak.stock_zh_a_spot_em()
                    dfo['代码'] = pd.to_numeric(dfo['代码'], errors='coerce')
                    dfo = dfo[dfo['代码'] < 700000]
                    dfo['secID'] = dfo['代码'].astype(str).apply(lambda x: (x.zfill(6) + '.XSHE' if x.startswith(('00', '30')) or len(x) < 6 else (x.zfill(6) + '.XSHG' if x.startswith(('60', '68')) else x.zfill(6))))
                    dfo = dfo[dfo['secID'].str.len() >= 10]
                    dfo = dfo.drop(columns=['代码'])
                    MktLimitGet = pd.read_csv(r'd:\Andy\gupiao\MktLimitGet.csv', encoding='GBK')
                    MktLimitGet = MktLimitGet[['secID', 'limitUpPrice']]
                    dfo_zhangting = dfo[['secID', '名称', '最新价']]
                    dfo_zhangting=pd.merge(dfo_zhangting, MktLimitGet, how='left', on=['secID'])
                    dfo_zhangting = dfo_zhangting[dfo_zhangting['最新价'] == dfo_zhangting['limitUpPrice']]
                    dfo_zhangting = dfo_zhangting[['secID', '名称']]
                    zhangting_all = pd.read_csv(r'd:\Andy\gupiao\zhangting_all_unique.csv')
                    zhangting_all.drop(['tradeDate','股票名称'], axis=1, inplace=True)
                    # 确保zhangting_all的'题材'列是字符串类型
                    if '题材' in zhangting_all.columns:
                        zhangting_all['题材'] = zhangting_all['题材'].astype('object')
                    dfo_zhangting=pd.merge(dfo_zhangting, zhangting_all, how='left', on=['secID'])
                    dfo_zhangting.to_csv(filename, index=False, encoding='utf-8-sig')
                    upload_file_to_ubuntu(filename, remote_dir_path, ubuntu_server_ip, ubuntu_username, ubuntu_password)
                    df = dfo_zhangting
                
                concepts_list = df['题材']
                # 使用列表推导式和split()方法分割字符串，并收集所有概念名称
                all_concepts = []
                for concept in concepts_list:
                    if isinstance(concept, str):
                        # 使用split()将字符串按空格分割成列表
                        concepts = concept.strip().split()  # strip()去除首尾空格
                        all_concepts.extend(concepts)  # extend将列表元素添加到all_concepts
                # 使用Counter统计每个概念名称出现的次数
                concept_counts = Counter(all_concepts)
                # 将统计结果转换为DataFrame
                concept_counts_df = pd.DataFrame(list(concept_counts.items()), columns=['题材', '涨停股出现次数'])
                concept_counts_df = concept_counts_df.sort_values(by='涨停股出现次数', ascending=False) #涨停股出现次数
                output_csv = r'D:\Andy\gupiao\zhangting涨停股题材次数统计.csv'
                lasttime_df0925 = pd.read_csv(r'D:\Andy\gupiao\zhangting涨停股题材次数统计0925.csv', encoding='utf-8-sig')
                lasttime_df0925 = lasttime_df0925[['题材', '涨停股出现次数']]
                lasttime_df0925 = lasttime_df0925.rename(columns={'涨停股出现次数': '涨停股出现次数_初始'})
                lasttime_df = pd.read_csv(output_csv, encoding='utf-8-sig')
                lasttime_df = lasttime_df[['题材', '涨停股出现次数']]
                lasttime_df = lasttime_df.rename(columns={'涨停股出现次数': '涨停股出现次数_上次'})
                concept_counts_df = pd.merge(concept_counts_df, lasttime_df, on='题材', how='left')
                # 将涨停股出现次数_上次的空值填充为0
                concept_counts_df['涨停股出现次数_上次'] = concept_counts_df['涨停股出现次数_上次'].fillna(0)
                concept_counts_df['比上次次数升降'] = concept_counts_df['涨停股出现次数'] - concept_counts_df['涨停股出现次数_上次']
                concept_counts_df = pd.merge(concept_counts_df, lasttime_df0925, on='题材', how='left')
                concept_counts_df['涨停股出现次数_初始'] = concept_counts_df['涨停股出现次数_初始'].fillna(0)
                concept_counts_df['比初始次数升降'] = concept_counts_df['涨停股出现次数'] - concept_counts_df['涨停股出现次数_初始']
                concept_counts_df.to_csv(output_csv, index=False, encoding='utf-8-sig')
                upload_file_to_ubuntu(output_csv, remote_dir_path, ubuntu_server_ip, ubuntu_username, ubuntu_password)
                current_time = datetime.datetime.now().time()
                if current_time < datetime.time(9, 30):
                    concept_counts_df.to_csv(r'D:\Andy\gupiao\zhangting涨停股题材次数统计0925.csv', index=False, encoding='utf-8-sig')
                    upload_file_to_ubuntu(r'D:\Andy\gupiao\zhangting涨停股题材次数统计0925.csv', remote_dir_path, ubuntu_server_ip, ubuntu_username, ubuntu_password)
            
        else:
            print("未找到id为'limituptable'的表格。")
            print("页面内容:")
            print(soup.prettify()[:1000])  # 打印前1000个字符以供检查
    
    except Exception as e:
        print(f"发生错误: {e}")
        dfo = ak.stock_zh_a_spot_em()
        dfo['代码'] = pd.to_numeric(dfo['代码'], errors='coerce')
        dfo = dfo[dfo['代码'] < 700000]
        dfo['secID'] = dfo['代码'].astype(str).apply(lambda x: (x.zfill(6) + '.XSHE' if x.startswith(('00', '30')) or len(x) < 6 else (x.zfill(6) + '.XSHG' if x.startswith(('60', '68')) else x.zfill(6))))
        dfo = dfo[dfo['secID'].str.len() >= 10]
        dfo = dfo.drop(columns=['代码'])
        dfo = dfo[(~dfo['名称'].str.contains('B')) & (~dfo['名称'].str.contains('ST'))]
        MktLimitGet = pd.read_csv(r'd:\Andy\gupiao\MktLimitGet.csv', encoding='GBK')
        MktLimitGet = MktLimitGet[['secID', 'limitUpPrice']]
        dfo_zhangting = dfo[['secID', '名称', '最新价']]
        dfo_zhangting=pd.merge(dfo_zhangting, MktLimitGet, how='left', on=['secID'])
        dfo_zhangting = dfo_zhangting[dfo_zhangting['最新价'] == dfo_zhangting['limitUpPrice']]
        dfo_zhangting = dfo_zhangting[['secID', '名称']]
        zhangting_all = pd.read_csv(r'd:\Andy\gupiao\zhangting_all_unique.csv')
        zhangting_all.drop(['tradeDate','股票名称'], axis=1, inplace=True)
        # 确保zhangting_all的'题材'列是字符串类型
        if '题材' in zhangting_all.columns:
            zhangting_all['题材'] = zhangting_all['题材'].astype('object')
        dfo_zhangting=pd.merge(dfo_zhangting, zhangting_all, how='left', on=['secID'])
        dfo_zhangting.to_csv(filename, index=False, encoding='utf-8-sig')
        upload_file_to_ubuntu(filename, remote_dir_path, ubuntu_server_ip, ubuntu_username, ubuntu_password)
        df = dfo_zhangting
        concepts_list = df['题材']
        # 使用列表推导式和split()方法分割字符串，并收集所有概念名称
        all_concepts = []
        for concept in concepts_list:
            if isinstance(concept, str):
                # 使用split()将字符串按空格分割成列表
                concepts = concept.strip().split()  # strip()去除首尾空格
                all_concepts.extend(concepts)  # extend将列表元素添加到all_concepts
        # 使用Counter统计每个概念名称出现的次数
        concept_counts = Counter(all_concepts)
        # 将统计结果转换为DataFrame
        concept_counts_df = pd.DataFrame(list(concept_counts.items()), columns=['题材', '涨停股出现次数'])
        concept_counts_df = concept_counts_df.sort_values(by='涨停股出现次数', ascending=False) #涨停股出现次数
        output_csv = r'D:\Andy\gupiao\zhangting涨停股题材次数统计.csv'
        lasttime_df0925 = pd.read_csv(r'D:\Andy\gupiao\zhangting涨停股题材次数统计0925.csv', encoding='utf-8-sig')
        lasttime_df0925 = lasttime_df0925[['题材', '涨停股出现次数']]
        lasttime_df0925 = lasttime_df0925.rename(columns={'涨停股出现次数': '涨停股出现次数_初始'})
        lasttime_df = pd.read_csv(output_csv, encoding='utf-8-sig')
        lasttime_df = lasttime_df[['题材', '涨停股出现次数']]
        lasttime_df = lasttime_df.rename(columns={'涨停股出现次数': '涨停股出现次数_上次'})
        concept_counts_df = pd.merge(concept_counts_df, lasttime_df, on='题材', how='left')
        # 将涨停股出现次数_上次的空值填充为0
        concept_counts_df['涨停股出现次数_上次'] = concept_counts_df['涨停股出现次数_上次'].fillna(0)
        concept_counts_df['比上次次数升降'] = concept_counts_df['涨停股出现次数'] - concept_counts_df['涨停股出现次数_上次']
        concept_counts_df = pd.merge(concept_counts_df, lasttime_df0925, on='题材', how='left')
        concept_counts_df['涨停股出现次数_初始'] = concept_counts_df['涨停股出现次数_初始'].fillna(0)
        concept_counts_df['比初始次数升降'] = concept_counts_df['涨停股出现次数'] - concept_counts_df['涨停股出现次数_初始']
        concept_counts_df.to_csv(output_csv, index=False, encoding='utf-8-sig')
        upload_file_to_ubuntu(output_csv, remote_dir_path, ubuntu_server_ip, ubuntu_username, ubuntu_password)
        current_time = datetime.datetime.now().time()
        if current_time < datetime.time(9, 30):
            concept_counts_df.to_csv(r'D:\Andy\gupiao\zhangting涨停股题材次数统计0925.csv', index=False, encoding='utf-8-sig')
            upload_file_to_ubuntu(r'D:\Andy\gupiao\zhangting涨停股题材次数统计0925.csv', remote_dir_path, ubuntu_server_ip, ubuntu_username, ubuntu_password)

    print("等待60秒")
    time.sleep(60)