import os
import pandas as pd
from datetime import datetime, timedelta
import requests

def load_data_efficiently(date):
    """只读取必要的列，减少内存使用"""
    needed_columns = {
        'equd': ['secID', 'secShortName', 'ticker', 'exchangeCD', 'chgPct', 'closePrice', 'lowestPrice', 'highestPrice', 'preClosePrice', 'actPreClosePrice', 'turnoverVol', 'turnoverRate', 'openPrice', 'turnoverValue', 'vwap'],
        'limit': ['secID', 'secShortName', 'limitUpPrice', 'limitDownPrice'],
        'factors': ['secID', 'MA5', 'MA10', 'MA20', 'MA60', 'EMA5', 'EMA10', 'EMA20', 'EMA60', 'RSI', 'KDJ_K', 'KDJ_D', 'KDJ_J', 'OBV', 'OBV6', 'OBV20', 'MACD', 'VEMA5', 'VEMA10', 'VEMA12', 'VEMA26', 'BollUp', 'BollDown', 'ATR6', 'ATR14', 'PSY', 'AR', 'BR', 'VR', 'BIAS5', 'BIAS10', 'BIAS20', 'BIAS60', 'ROC6', 'ROC20', 'EMV6', 'EMV14', 'MTM', 'MTMMA', 'PVT', 'PVT6', 'PVT12', 'TRIX5', 'TRIX10', 'MFI', 'WVAD', 'ChaikinOscillator', 'ChaikinVolatility', 'ASI', 'ARBR', 'CR20', 'ADTM', 'DDI', 'DEA', 'DIFF', 'BBI', 'TRIX5', 'TRIX10', 'UOS', 'MA10RegressCoeff12', 'MA10RegressCoeff6']
    }


    try:
        equd_df = pd.read_csv(os.path.join(equd_dir, f"{date}.csv"),
                             encoding='gbk', usecols=needed_columns['equd'])
        limit_df = pd.read_csv(os.path.join(limit_dir, f"{date}.csv"),
                              encoding='gbk', usecols=needed_columns['limit'])
        factors_df = pd.read_csv(os.path.join(mkt_stock_factors_dir, f"{date}.csv"),
                                encoding='gbk', usecols=needed_columns['factors'])
        return pd.merge(pd.merge(equd_df, limit_df, on=["secID", "secShortName"]),
                       factors_df, on=["secID"])
    except Exception as e:
        print(f"读取{date}数据出错: {e}")
        return None

def process_stock_data(group):
    """处理单个股票的数据，选出第一天涨停的股票"""
    if len(group) < 5:  # 至少需要5天数据（涨停当天和涨停前四天）
        return []

    results = []
    group = group.reset_index(drop=True)

    print(f"\n处理股票: {group.iloc[0]['secShortName']} ({group.iloc[0]['secID']})")
    print(f"数据天数: {len(group)}")

    found_patterns = 0
    # 从第5天开始，确保有足够的历史数据（涨停前4天）
    for i in range(4, len(group)-2):
        # 判断是否涨停：收盘价等于涨停价
        if group.loc[i, "closePrice"] == group.loc[i, "limitUpPrice"] :
            # 确保前四天都不是涨停
            if (group.loc[i-1, "closePrice"] == group.loc[i-1, "limitUpPrice"] and
                group.loc[i-2, "closePrice"] != group.loc[i-2, "limitUpPrice"] and
                group.loc[i-3, "closePrice"] != group.loc[i-3, "limitUpPrice"] and
                group.loc[i-4, "closePrice"] != group.loc[i-4, "limitUpPrice"]):
                # 确保有足够的后续数据
                if i+2 <= len(group) and group.loc[i+2, "highestPrice"] > 0 and group.loc[i+1, "openPrice"] != group.loc[i+1, "limitUpPrice"] and group.loc[i+1, "openPrice"] > 0:
                    # 涨停当天和涨停前四天的数据已经通过索引获取

                    # 买入价设为涨停第二天的开盘价
                    buy_price = group.loc[i+1, "openPrice"]+group.loc[i+2, "preClosePrice"]-group.loc[i+2, "actPreClosePrice"]

                    # 卖出价设为涨停第三天的最高价
                    sell_price = group.loc[i+2, "highestPrice"]

                    # 计算收益率
                    profit_rate = (sell_price / buy_price - 1) * 100

                    # 创建结果字典，包含基本信息
                    result = {
                        "secID": group.iloc[0]["secID"],
                        "secShortName": group.iloc[0]["secShortName"],
                        "涨停日期": group.loc[i, "date"],
                        "买入价": buy_price,
                        "卖出价": sell_price,
                        "收益率": profit_rate
                    }

                    # 添加每一天的数据
                    day_names = {
                        # -4: "涨停前4天",
                        # -3: "涨停前3天",
                        # -2: "涨停前2天",
                        # -1: "涨停前1天",
                        0: "涨停当天"
                    }

                    # 首先添加原始数据（为了参考）
                    result['涨停当天_closePrice'] = group.loc[i, 'closePrice']
                    result['涨停当天_limitUpPrice'] = group.loc[i, 'limitUpPrice']
                    result['买入当天开盘涨幅'] = (group.loc[i+1, 'openPrice'] / (group.loc[i, 'closePrice'] + group.loc[i+1, 'preClosePrice'] - group.loc[i+1, 'actPreClosePrice'] ) - 1) * 100

                    # 添加equd中的列的比值
                    equd_cols = ['turnoverVol', 'turnoverRate', 'turnoverValue', 'vwap']
                    for day_offset, day_name in day_names.items():
                        if day_offset > -4:  # 从涨停前3天开始，因为需要前一天的数据计算比值
                            for col in equd_cols:
                                if col in group.columns:
                                    current_value = group.loc[i+day_offset, col]
                                    prev_value = group.loc[i+day_offset-1, col]
                                    if prev_value != 0:  # 避免除零错误
                                        ratio = current_value / prev_value
                                        result[f"{day_name}_{col}_ratio"] = ratio

                    # 添加factors中的列的比值
                    factor_cols = ['MA5', 'MA10', 'MA20', 'MA60', 'EMA5', 'EMA10', 'EMA20', 'EMA60', 'RSI', 'KDJ_K', 'KDJ_D', 'KDJ_J', 'OBV', 'OBV6', 'OBV20', 'MACD', 'VEMA5', 'VEMA10', 'VEMA12', 'VEMA26', 'BollUp', 'BollDown', 'ATR6', 'ATR14', 'PSY', 'AR', 'BR', 'VR', 'BIAS5', 'BIAS10', 'BIAS20', 'BIAS60', 'ROC6', 'ROC20', 'EMV6', 'EMV14', 'MTM', 'MTMMA', 'PVT', 'PVT6', 'PVT12', 'TRIX5', 'TRIX10', 'MFI', 'WVAD', 'ChaikinOscillator', 'ChaikinVolatility', 'ASI', 'ARBR', 'CR20', 'ADTM', 'DDI', 'DEA', 'DIFF', 'BBI', 'UOS', 'MA10RegressCoeff12', 'MA10RegressCoeff6']
                    for day_offset, day_name in day_names.items():
                        if day_offset > -4:  # 从涨停前3天开始
                            for col in factor_cols:
                                if col in group.columns:
                                    current_value = group.loc[i+day_offset, col]
                                    prev_value = group.loc[i+day_offset-1, col]
                                    if prev_value != 0 and not pd.isna(current_value) and not pd.isna(prev_value):  # 避免除零错误和NaN
                                        ratio = current_value / prev_value
                                        result[f"{day_name}_{col}_ratio"] = ratio

                    found_patterns += 1
                    results.append(result)

    print(f"找到符合条件的模式数量: {found_patterns}")
    return results

# 2. 然后是全局变量定义
token = '44055711dfeb4124a8df9531f8dd2f59'
equd_dir = "d:\\Andy\\Data\\MktEqudGet\\"
limit_dir = "d:\\Andy\\Data\\MktLimitGet\\"
mkt_stock_factors_dir = r'd:\Andy\Data\MktStockFactorsOneDayGet'

# 3. 主程序代码
start_date = "20160101"
end_date = "20250416"
print("Start date:", start_date)
print("End date:", end_date)
output_file = 'd:\\Andy\\gupiao\\analysis\\2days_zhangting_analysis_' + start_date + '_' + end_date + '.csv'

# 获取日期范围内的交易日
trading_dates = sorted([f.split('.')[0] for f in os.listdir(equd_dir)
                      if f.endswith('.csv') and start_date <= f.split('.')[0] <= end_date])

print(f"找到交易日数量: {len(trading_dates)}")

# 创建一个空的DataFrame来存储所有日期的数据
all_data = pd.DataFrame()

# 读取所有日期的数据并合并
for date in trading_dates:
    daily_data = load_data_efficiently(date)
    if daily_data is not None:
        daily_data['date'] = date  # 添加日期列
        all_data = pd.concat([all_data, daily_data], ignore_index=True)

# 应用过滤条件
filtered_data = all_data[
    (all_data['ticker'] < 700000) &
    (all_data['exchangeCD'].isin(["XSHE", "XSHG"])) &
    (~all_data['secShortName'].str.contains('B|ST')) &
    (all_data['closePrice'] > 3)
]

all_results = []
# 按股票代码分组，这样每个group就会包含该股票在整个日期范围内的所有数据
for secID, group in filtered_data.groupby("secID"):
    # 按日期排序
    group = group.sort_values('date')
    results = process_stock_data(group)
    all_results.extend(results)

print("\n最终结果统计:")
print(f"找到的总模式数量: {len(all_results)}")

# 将结果转换为DataFrame并保存到CSV文件
results_df = pd.DataFrame(all_results)
if not results_df.empty:
    # 保存结果
    results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n结果已保存到: {output_file}")