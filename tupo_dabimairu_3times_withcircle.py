from datetime import datetime, timedelta, date, time
import time as time_sleep
import os
import pandas as pd
from collections import deque, defaultdict
import requests
import warnings

warnings.filterwarnings('ignore')
token = '44055711dfeb4124a8df9531f8dd2f59'  # 在pushplus网站中可以找到
save_path = r'd:\Andy\gupiao\Data\Minutes_dabi'
predictDate = (date.today() - timedelta(days=0)).strftime("%Y%m%d")
print(predictDate)
dir_path = save_path + '/' + predictDate + '/'
dir_path2 = r'd:/Andy/gupiao/Data/Minutes' + '/' + predictDate + '/'

# 用于记录已发送的股票
sent_secIDs = {}

def send_message(title, message):
    """发送消息到pushplus和ntfy.sh"""
    try:
        requests.post("https://ntfy.sh/Xiaoertan", data=message.encode(encoding='utf-8'))
        url = f'http://www.pushplus.plus/send?token={token}&title={title}&content={message}'
        requests.get(url)
    except Exception as e:
        print("发送消息时发生错误:", e)

def count_csv_files(path):
    """统计指定目录下的CSV文件数量"""
    return sum(1 for filename in os.listdir(path) if filename.lower().endswith('.csv'))

def get_time_slot(time_str):
    """将时间字符串转换为分钟时间槽"""
    hour, minute, second = map(int, time_str.split(':'))
    total_minutes = hour * 60 + minute
    return total_minutes // 1

while True:
    # 创建字典来记录每个股票在不同时间槽的出现情况
    stock_time_slots = defaultdict(set)
    # 创建字典来存储每个股票在每个时间点的数据，使用时间作为key避免重复
    stock_time_data = defaultdict(dict)

    file_index = 1
    k = count_csv_files(dir_path)

    while k > 0:
        # print(file_index)
        file_path = f'{dir_path}/{predictDate}_{file_index:03d}.csv'
        
        if os.path.exists(file_path):
            stock_changes_em_df = pd.read_csv(file_path)
            N_shape_pool = pd.read_csv(r'd:\Andy\gupiao\N形待选_today.csv')
            # 合并N形待选池
            stock_changes_em_df_dabi = stock_changes_em_df.merge(N_shape_pool, on='secID', how='left')
            stock_changes_em_df_dabi.dropna(subset=['secShortName'], inplace=True)
            
            # 筛选符合条件的股票
            daemairu = stock_changes_em_df_dabi[(stock_changes_em_df_dabi['buyin'] > 600)]
            daemairu = daemairu[(daemairu['chgPct'] > 1) & (daemairu['chgPct'] < 8)]
            # daemairu = daemairu[daemairu['收盘价格位置'].str.startswith('5日')]
            # daemairu = daemairu[daemairu['时间'] >= '09:32:00']
            
            # 更新每只股票的出现记录和数据
            for _, row in daemairu.iterrows():
                secID = row['secID']
                time_str = row['时间']
                time_slot = get_time_slot(time_str)
                
                # 记录该股票在这个时间槽的出现
                stock_time_slots[secID].add(time_slot)
                
                # 使用时间作为key来存储数据，避免重复
                stock_time_data[secID][time_str] = row.to_dict()
        
        k -= 1
        file_index += 1

    # 创建结果DataFrame
    all_records = []
    for secID, time_slots in stock_time_slots.items():
        if len(time_slots) >= 3:  # 只处理在不同时间槽出现3次及以上的股票
            # 获取该股票的所有时间点记录
            records = list(stock_time_data[secID].values())  # 使用values()来获取唯一的记录
            # 为每条记录添加出现次数
            for record in records:
                record['出现次数'] = len(time_slots)
                all_records.append(record)

    # 转换为DataFrame并按时间排序
    filtered_stocks = pd.DataFrame(all_records)
    if not filtered_stocks.empty:
        # 确保按secID和时间排序，并去除完全重复的行
        filtered_stocks = filtered_stocks.sort_values(['secID', '时间']).drop_duplicates()

    # 保存符合条件的股票到founded_N型.csv
    if len(filtered_stocks) > 0:
        
        # 保存结果
        filtered_stocks.to_csv(f'{dir_path2}/zhangting/founded_N型_' + predictDate + '.csv', index=False, encoding='utf-8-sig')
        
        # 打印统计信息
        unique_stocks = filtered_stocks['secID'].nunique()
        total_records = len(filtered_stocks)
        print(f"处理完成，共找到{unique_stocks}支符合条件的股票，总计{total_records}条记录")
        # filtered_stocks中相同secID的记录只保留最后一条
        filtered_stocks = filtered_stocks.drop_duplicates(subset=['secID'], keep='last')
        
        # 筛选出未发送过的股票
        new_stocks = filtered_stocks[~filtered_stocks['secID'].isin(sent_secIDs)]
        
        if len(new_stocks) > 0:
            current_time = datetime.now().time()
            if current_time < time(14, 50) and current_time > time(9, 32):
                combined_string = '题材_'+'时间_'+'买入额_'+'涨跌幅_'+'价格\n'+'\n'.join(
                        new_stocks['secID'].astype(str) + "_" +
                        new_stocks['secShortName'] + "_" +
                        new_stocks['题材'].astype(str) + "_" +
                        new_stocks['时间'].astype(str) + "_" +
                        new_stocks['buyin'].astype(str) + "_" +
                        new_stocks['chgPct'].astype(str) + "_" +
                        new_stocks['Price'].astype(str)
                    )
                send_message("N型三点火", combined_string)
                
                # 更新已发送记录
                for secID in new_stocks['secID']:
                    sent_secIDs[secID] = True

    # 检查当前时间，如果超过15:00或者在11:30-13:00之间就退出
    current_time = datetime.now().time()
    if current_time > time(15, 0) or (current_time > time(11, 30) and current_time < time(13, 0)):
        break
        
    # 休眠59秒
    time_sleep.sleep(59)