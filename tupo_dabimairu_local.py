import datetime
from datetime import datetime, timedelta, date
import time
import os
import pandas as pd
from collections import deque
import warnings

warnings.filterwarnings('ignore')

# 设置保存路径和文件
save_path = r'd:\Andy\gupiao\Data\Minutes_dabi'
predictDate = (date.today() - timedelta(days=0)).strftime("%Y%m%d")
dir_path = save_path + '/' + predictDate + '/'
Nxing_date = (datetime.strptime(predictDate, '%Y%m%d') - timedelta(days=1)).strftime("%Y%m%d")

# 创建文件夹
if not os.path.exists(dir_path):
    os.makedirs(dir_path)
    os.makedirs(save_path + '/' + predictDate + '/zhangting/')

def count_csv_files(path):
    """统计指定目录下的CSV文件数量"""
    return sum(1 for filename in os.listdir(path) if filename.lower().endswith('.csv'))

file_index = 1

k = count_csv_files(dir_path)
while k > 0:
    # 从本地文件读取大笔买入数据
    # stock_changes_em_df = pd.read_csv(f'{dir_path}/{predictDate}_{file_index:03d}.csv')
    print(file_index)
    file_path = f'{dir_path}/{predictDate}_{file_index:03d}.csv'
    if not os.path.exists(file_path):
        file_index += 1
        continue
    stock_changes_em_df = pd.read_csv(file_path)
    stock_changes_em_df_keep = stock_changes_em_df

    # sum
    grouped_sum = stock_changes_em_df.groupby('名称')['buyin'].sum()
    grouped_sum_df = grouped_sum.reset_index()
    stock_changes_em_df = stock_changes_em_df.merge(grouped_sum_df, on='名称', how='left')
    stock_changes_em_df['数量'] = stock_changes_em_df.groupby('名称').transform('size')
    stock_changes_em_df = stock_changes_em_df.drop_duplicates(subset='名称', keep='first')
    stock_changes_em_df = stock_changes_em_df.sort_values(by='buyin_y', ascending=False)
    stock_changes_em_df['buyin_y'] = round(stock_changes_em_df['buyin_y'], 2)
    stock_changes_em_df['buyin_x'] = round(stock_changes_em_df['buyin_x'], 2)
    stock_changes_em_df = stock_changes_em_df.rename(columns={'buyin_y': '总买入金额'})
    stock_changes_em_df = stock_changes_em_df.rename(columns={'buyin_x': '现买入金额'})

    # 合并版块信息
    stock_board_concept_name_ths_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_ths_merged.csv', encoding='utf-8-sig')
    stock_board_concept_name_ths_merged.drop(['secShortName'], axis=1, inplace=True)
    stock_board_concept_name_em_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_em_merged.csv', encoding='utf-8-sig')
    stock_board_concept_name_em_merged['secID'] = stock_board_concept_name_em_merged['代码'].astype(str).str.zfill(6).apply(lambda x: x + '.XSHE' if x.startswith(('00', '30')) else (x + '.XSHG' if x.startswith(('60', '68')) else x))
    stock_board_concept_name_em_merged.drop(['序号','代码','secShortName_x','最新价','涨跌幅', '涨跌额', '成交量', '成交额', '振幅', '最高', '最低', '今开', '昨收', '换手率', '市盈率-动态', '市净率'], axis=1, inplace=True)
    stock_changes_em_df=pd.merge(stock_changes_em_df, stock_board_concept_name_ths_merged, how='left', on=['secID'])
    stock_changes_em_df=pd.merge(stock_changes_em_df, stock_board_concept_name_em_merged, how='left', on=['secID'])

    stock_changes_em_df['总买入金额'].fillna(0, inplace=True)

    # 获取市值数据
    todaydate = (date.today() - timedelta(days=1)).strftime("%Y%m%d")
    path = os.path.exists(r'd:\Andy\Data\MktEqudGet\\'+todaydate+'.csv')
    while path != True:
        todaydate=(datetime.strptime(todaydate, '%Y%m%d')-timedelta(days = 1)).strftime('%Y%m%d')
        path = os.path.exists(r'd:\Andy\Data\MktEqudGet\\'+todaydate+'.csv')
    shizhi = pd.read_csv(r'd:\Andy\Data\MktEqudGet\\'+todaydate+'.csv', encoding='gbk')
    shizhi = shizhi[['secShortName','negMarketValue']]
    shizhi = shizhi.rename(columns={'secShortName': '名称'})
    stock_changes_em_df = stock_changes_em_df.merge(shizhi, on='名称', how='left')
    stock_changes_em_df['总买入占比'] = round(stock_changes_em_df['总买入金额']*10000 / stock_changes_em_df['negMarketValue'], 2)

    # 加入融资融券信息
    rongzimingxi = pd.read_csv(r'd:\Andy\Data\融资融券明细\融资融券明细'+todaydate+'.csv')
    rongzimingxi = rongzimingxi.rename(columns={'secShortName': '名称'})
    rongzimingxi['融资买入额'] = round(rongzimingxi['融资买入额']/10000, 2)
    rongzimingxi['融资余额'] = round(rongzimingxi['融资余额']/10000, 2)
    stock_changes_em_df = stock_changes_em_df.merge(rongzimingxi, on='名称', how='left')

    # 大笔买入
    # 先判断N形待选池是否存在
    while not os.path.exists(r'd:/Andy/gupiao/N形待选_today.csv'):
        Nxing_date = (datetime.strptime(Nxing_date, '%Y%m%d') - timedelta(days=1)).strftime("%Y%m%d")
    N_shape_pool = pd.read_csv(r'd:/Andy/gupiao/N形待选_today.csv')
    stock_changes_em_df_dabi = stock_changes_em_df_keep.merge(N_shape_pool, on='secID', how='left')
    stock_changes_em_df_dabi.dropna(subset=['secShortName'], inplace=True)
    daemairu = stock_changes_em_df_dabi[(stock_changes_em_df_dabi['buyin'] > 600)]
    daemairu = daemairu[(daemairu['chgPct'] > 1) & (daemairu['chgPct'] < 8)]
    daemairu = daemairu.drop_duplicates(subset='secID', keep='last')

    stock_changes_em_df['today_sel'] = stock_changes_em_df['secID'].apply(lambda x: 1 if x in daemairu['secID'].values else 0)
    csv_file_path = r'd:\Andy\gupiao\大笔买入'+predictDate+'_sum.csv'
    stock_changes_em_df.to_csv(csv_file_path, index=False, encoding='utf-8-sig')

    stock_changes_em_df_sel = stock_changes_em_df[['secID', '现买入金额', '总买入金额', '数量', '总买入占比']]
    daemairu = daemairu.merge(stock_changes_em_df_sel, on='secID', how='left')

    if len(daemairu)>0:
        file_path3d_da = f'founded_N型_{predictDate}.csv'
        if not os.path.exists(file_path3d_da):
            daemairu.to_csv(file_path3d_da, index=False, encoding='utf-8-sig')
        else:
            existing_data_da = pd.read_csv(file_path3d_da)
            updated_data_da = pd.concat([existing_data_da, daemairu], ignore_index=True)
            
            # 数据清理和验证
            updated_data_da['时间'] = updated_data_da['时间'].astype(str).str.strip()
            updated_data_da['secID'] = updated_data_da['secID'].astype(str).str.strip()
            
            # 去重前的记录数
            before_drop = len(updated_data_da)
            
            # 删除相同'时间'且相同'secID'的行
            updated_data_da = updated_data_da.drop_duplicates(subset=['secID', '时间'], keep='first')
            
            # 去重后的记录数
            after_drop = len(updated_data_da)
            print(f"去重前记录数: {before_drop}, 去重后记录数: {after_drop}, 删除了 {before_drop - after_drop} 条重复记录")
            
            # 保存去重后的完整数据
            updated_data_da.to_csv(file_path3d_da, index=False, encoding='utf-8-sig')
            
            # 统计每个股票在不同时间点出现的次数
            stock_counts = updated_data_da.groupby('secID').size()
            # 找出出现3次及以上的股票
            qualified_stocks = stock_counts[stock_counts >= 3].index
            print(f"出现3次及以上的股票数量: {len(qualified_stocks)}")
            
            # 如果有符合条件的股票，保存到新文件
            if len(qualified_stocks) > 0:
                # 筛选出这些股票的所有记录
                three_times_stocks = updated_data_da[updated_data_da['secID'].isin(qualified_stocks)]
                # 按股票代码和时间排序
                three_times_stocks = three_times_stocks.sort_values(['secID', '时间'])
                # 保存结果
                three_times_stocks.to_csv(f'founded_N型_3次_{predictDate}.csv', index=False, encoding='utf-8-sig')
                print(f"发现 {len(qualified_stocks)} 只股票出现3次及以上：")
                # 打印这些股票的基本信息
                summary = three_times_stocks.groupby('secID').agg({
                    '名称': 'first',
                    '时间': 'count'
                }).rename(columns={'时间': '出现次数'})
                print(summary)

    k -= 1
    file_index += 1
