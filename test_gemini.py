import requests
import json


base_url = "https://fromozu-geba.hf.space/v1/chat/completions"

# 设置请求头  https://fromozu-geba.hf.space/ 1qA!2wS@3eD#
# https://v2.voct.top/v1/chat/completions"   Bearer fo-ubdtoUZMOhHt2NuO2tlNS2bx4xsXLHKt
headers = {
    "Authorization": f"Bearer 1qA!2wS@3eD#",
    "Content-Type": "application/json"
}

# 请求体
payload = {
    "messages": [
        {
            "role": "user",
            "content": "大便形状是否能反应健康状况"
        }
    ],
    "model": "gemini-2.0-flash",
    "temperature": 0.7,
    "stream": True,  # 流式响应
    "tools": [],
    "max_tokens": 8192,
    "stop": [],
    "top_p": 0.9,
    "top_k": 40
}

# 方案1：处理流式响应
def handle_streaming_response():
    # 发送POST请求，设置stream=True以获取流式响应
    with requests.post(base_url, headers=headers, json=payload, stream=True) as response:
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            full_content = ""
            
            # 逐行处理SSE响应
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    # 跳过data: 前缀
                    if line.startswith("data: "):
                        if line == "data: [DONE]":
                            break
                        
                        try:
                            # 解析JSON数据
                            data = json.loads(line[6:])  # 去掉"data: "前缀
                            if "choices" in data and len(data["choices"]) > 0:
                                delta = data["choices"][0].get("delta", {})
                                content = delta.get("content", "")
                                if content:
                                    # print(content, end="", flush=True)
                                    full_content += content
                        except json.JSONDecodeError as e:
                            print(f"JSON解析错误: {e}")
            
            print("\n\n完整回答:")
            print(full_content)
        else:
            print(f"请求失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")

# 方案2：使用非流式响应
def handle_non_streaming_response():
    # 创建一个新的payload，禁用流式响应
    non_stream_payload = payload.copy()
    non_stream_payload["stream"] = False
    
    # 发送POST请求
    response = requests.post(base_url, headers=headers, json=non_stream_payload)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        # 获取助手的回复内容
        assistant_message = result['choices'][0]['message']['content']
        
        print("\n聊天完成请求结果:")
        print(f"\n问题：{payload['messages'][0]['content']}")
        print("\n回答：")
        print(assistant_message)
    else:
        print(f"请求失败，状态码: {response.status_code}")
        print(f"错误信息: {response.text}")

# 选择使用哪种方式处理响应
if payload["stream"]:
    handle_streaming_response()
else:
    handle_non_streaming_response()