#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的zhuazhangting.py程序（单次运行版本）
"""

from playwright.sync_api import sync_playwright
from bs4 import BeautifulSoup
import datetime
import pandas as pd
import csv
import os
import time
from collections import Counter
import akshare as ak
import paramiko

def upload_file_to_ubuntu(local_file_path, remote_dir_path, server_ip, username, password):
    try:
        # 建立SSH传输连接
        transport = paramiko.Transport((server_ip, 22))
        transport.connect(username=username, password=password)

        # 创建SFTP客户端
        sftp = paramiko.SFTPClient.from_transport(transport)

        # 检查远程目录是否存在,不存在则创建
        try:
            sftp.stat(remote_dir_path)
        except IOError:
            sftp.mkdir(remote_dir_path)

        # 获取本地文件名
        local_filename = os.path.basename(local_file_path)
        
        # 构建远程文件完整路径
        remote_file_path = os.path.join(remote_dir_path, local_filename)

        # 上传文件
        sftp.put(local_file_path, remote_file_path)
        print(f"文件 '{local_file_path}' 已成功上传到远程服务器 '{remote_file_path}'")

        # 关闭连接
        sftp.close()
        transport.close()

    except Exception as e:
        print(f"上传文件时出错: {e}")

def test_single_run():
    """测试单次运行程序"""
    print("=== 测试修复后的zhuazhangting.py程序（单次运行） ===")
    
    remote_dir_path = "/var/www/html/files/"
    ubuntu_server_ip = "*************"
    ubuntu_username = "user1/root"
    ubuntu_password = "@uSJVBqSCP2E"

    # 访问新的网页
    url = "http://www.wuylh.com/zthelper/ztindex.jsp"
    
    try:
        # 使用Playwright启动Edge浏览器
        with sync_playwright() as p:
            print("启动Edge浏览器...")
            # 启动Edge浏览器（无头模式）
            browser = p.chromium.launch(
                headless=True,
                args=[
                    "--disable-gpu",
                    "--no-sandbox", 
                    "--disable-dev-shm-usage",
                    "--disable-extensions",
                    "--disable-background-networking",
                    "--disable-sync",
                    "--disable-translate",
                    "--ignore-ssl-errors-on-localhost",
                    "--ignore-certificate-errors"
                ]
            )
            
            # 创建新的页面
            page = browser.new_page()
            
            print(f"访问网页: {url}")
            # 访问网页
            page.goto(url)
            time.sleep(6)
            
            # 等待表格加载完成
            page.wait_for_selector("#limituptable", timeout=20000)
            
            # 等待加载提示消失
            page.wait_for_selector("#loading", state="hidden", timeout=20000)
            
            # 额外等待一些时间，确保数据完全加载
            time.sleep(5)
        
            # 获取页面源代码
            page_source = page.content()
            
            # 关闭浏览器
            browser.close()
    
        print("解析页面数据...")
        # 使用BeautifulSoup解析页面源代码
        soup = BeautifulSoup(page_source, 'html.parser')
    
        # 提取表格数据
        table = soup.find('table', id='limituptable')
        if table:
            rows = table.find_all('tr')
    
            # 准备CSV文件
            filename = r'D:\Andy\gupiao\zhangting_test.csv'
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入表头
                headers = [th.text.strip() for th in rows[0].find_all('th')]
                writer.writerow(headers)
                
                # 写入数据行
                for row in rows[1:]:
                    data = [td.text.strip() for td in row.find_all('td')]
                    if data:  # 只有当数据行非空时才处理
                        data = data[:-1]  # 移除最后一个元素
                        writer.writerow(data)
            
            print(f"✅ 成功保存 {len(rows)-1} 行数据到 {filename}")
            
            # 读取CSV文件进行简单处理测试
            df = pd.read_csv(filename, encoding='utf-8-sig')
            if len(df) > 0:  
                print(f"✅ 成功读取CSV文件，共 {len(df)} 行数据")
                print("前5行数据预览:")
                print(df.head())
                
                # 测试数据类型处理
                if '题材' in df.columns:
                    df['题材'] = df['题材'].astype('object')
                    print("✅ 题材列数据类型处理正常")
                
                print("✅ 程序运行成功，无错误日志！")
                return True
            
        else:
            print("❌ 未找到id为'limituptable'的表格")
            return False
    
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return False

if __name__ == "__main__":
    success = test_single_run()
    
    if success:
        print("\n🎉 测试完成！新的Playwright + Edge实现工作正常，错误日志显著减少！")
    else:
        print("\n⚠️ 测试失败，需要检查网络连接或网站状态。")
