import akshare as ak
import datetime
import time
import os
import sys
import logging
import pandas as pd
import pymysql
from sqlalchemy import create_engine, text, MetaData, Table, Column, String, Integer, Numeric, Date, DateTime, Text, Boolean
from sqlalchemy.dialects.mysql import LONGTEXT

import requests
import warnings
import boto3
from botocore.config import Config

warnings.filterwarnings('ignore')

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': 'stock-31bk-stock-31bankuai.c.aivencloud.com',
    'port': 26991,
    'user': 'avnadmin',
    'password': 'AVNS_daq_tCJ6LP2VwbS_633',
    'database': 'defaultdb',
    'charset': 'utf8mb4'
}

# 数据库表名
TABLE_NAME = 'large_buy_orders'

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('large_buy_analysis.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)



# 设置保存路径和文件
token = '44055711dfeb4124a8df9531f8dd2f59'  # 在pushplus网站中可以找到
save_path = r'd:\Andy\gupiao\Data\Minutes_dabi'
predictDate = (datetime.date.today() - datetime.timedelta(days=0)).strftime("%Y%m%d")
dir_path = save_path + '/' + predictDate + '/'
dir_path2 = r'd:/Andy/gupiao/Data/Minutes' + '/' + predictDate + '/'

# 创建文件夹
if not os.path.exists(dir_path):
    os.makedirs(dir_path)
    os.makedirs(save_path + '/' + predictDate + '/zhangting/')

def count_csv_files(path):
    """统计指定目录下的CSV文件数量"""
    return sum(1 for filename in os.listdir(path) if filename.lower().endswith('.csv'))

file_index = count_csv_files(dir_path) + 1

def send_message(title, message):
    """发送消息到pushplus和ntfy.sh"""
    try:
        requests.post("https://ntfy.sh/Xiaoertan", data=message.encode(encoding='utf-8'))
        url = f'http://www.pushplus.plus/send?token={token}&title={title}&content={message}'
        requests.get(url)
    except Exception as e:
        print("发送消息时发生错误:", e)

def create_database_engine():
    """创建数据库引擎"""
    try:
        connection_url = (
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}"
            f"@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['database']}"
            f"?charset={MYSQL_CONFIG['charset']}&ssl_verify_cert=false&ssl_verify_identity=false"
        )

        engine = create_engine(
            connection_url,
            pool_size=5,
            max_overflow=10,
            pool_timeout=10,
            pool_recycle=3600,
            pool_pre_ping=True,
            echo=False,
            connect_args={
                'connect_timeout': 10,
                'read_timeout': 60,
                'write_timeout': 60,
                'charset': 'utf8mb4'
            }
        )

        # 测试连接
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.fetchone():
                logger.info("✅ 数据库连接成功")
                return engine
            else:
                raise Exception("数据库连接测试失败")

    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        raise

def get_table_field_lengths(engine):
    """获取数据库表的字段长度限制"""
    try:
        logger.info(f"🔍 正在获取表字段长度限制: {TABLE_NAME}")
        field_lengths = {}

        with engine.connect() as conn:
            result = conn.execute(text(f"DESCRIBE {TABLE_NAME}"))
            columns = result.fetchall()

            for col in columns:
                field_name = col[0]
                field_type = col[1]

                # 解析字段长度
                max_length = None
                if 'varchar' in field_type.lower():
                    # 提取varchar长度
                    start = field_type.find('(')
                    end = field_type.find(')')
                    if start != -1 and end != -1:
                        max_length = int(field_type[start+1:end])
                elif 'text' in field_type.lower():
                    if 'longtext' in field_type.lower():
                        max_length = 4294967295  # LONGTEXT最大长度
                    elif 'mediumtext' in field_type.lower():
                        max_length = 16777215    # MEDIUMTEXT最大长度
                    else:
                        max_length = 65535       # TEXT最大长度

                if max_length:
                    field_lengths[field_name] = max_length

            logger.info(f"✅ 获取到 {len(field_lengths)} 个字段的长度限制")
            return field_lengths

    except Exception as e:
        logger.error(f"❌ 获取字段长度限制失败: {e}")
        return {}

def smart_truncate_text(text, max_length, field_name=""):
    """智能截断文本，支持去重和语义保持"""
    if not text or pd.isna(text):
        return text

    text = str(text).strip()
    if len(text) <= max_length:
        return text

    # 记录原始长度
    original_length = len(text)

    # 对于包含分隔符的字段，尝试去重
    separators = [' ', '，', ',', '；', ';', '、', '|']
    has_separator = any(sep in text for sep in separators)

    if has_separator:
        # 尝试去重处理
        for sep in separators:
            if sep in text:
                parts = [part.strip() for part in text.split(sep) if part.strip()]
                # 去重但保持顺序
                unique_parts = []
                seen = set()
                for part in parts:
                    if part not in seen:
                        unique_parts.append(part)
                        seen.add(part)

                deduplicated_text = sep.join(unique_parts)
                if len(deduplicated_text) <= max_length:
                    logger.debug(f"🔧 字段 {field_name} 去重成功: {original_length} -> {len(deduplicated_text)} 字符")
                    return deduplicated_text
                else:
                    text = deduplicated_text
                break

    # 如果去重后仍然超长，进行智能截断
    if len(text) > max_length:
        # 保留3个字符用于"..."后缀
        truncate_length = max_length - 3
        truncated_text = text[:truncate_length] + "..."
        logger.debug(f"✂️ 字段 {field_name} 截断: {original_length} -> {len(truncated_text)} 字符")
        return truncated_text

    return text

def clear_table_data(engine):
    """清空large_buy_orders表中的所有数据"""
    try:
        logger.info(f"🗑️ 正在清空表: {TABLE_NAME}")
        with engine.connect() as conn:
            conn.execute(text(f"TRUNCATE TABLE {TABLE_NAME}"))
            conn.commit()
            logger.info(f"✅ 表数据清空成功: {TABLE_NAME}")
    except Exception as e:
        logger.error(f"❌ 清空表数据失败: {e}")
        raise

def import_csv_to_mysql(engine, csv_file_path):
    """将CSV文件导入到MySQL数据库"""
    try:
        logger.info(f"📤 开始导入CSV文件到数据库: {csv_file_path}")

        # 读取CSV文件
        if not os.path.exists(csv_file_path):
            raise FileNotFoundError(f"CSV文件不存在: {csv_file_path}")

        df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
        logger.info(f"📊 CSV文件读取成功，共 {len(df)} 行数据")

        if len(df) == 0:
            logger.warning("⚠️ CSV文件为空，跳过导入")
            return 0

        # 清理列名
        df_clean = df.copy()
        df_clean.columns = [
            col.replace('(', '_').replace(')', '_').replace('（', '_').replace('）', '_')
               .replace(' ', '_').replace('-', '_').replace('.', '_')
            for col in df_clean.columns
        ]

        # 添加时间戳
        df_clean['created_at'] = datetime.datetime.now()
        df_clean['updated_at'] = datetime.datetime.now()

        # 获取数据库字段长度限制
        field_lengths = get_table_field_lengths(engine)

        # 数据预处理：检查和截断超长字段
        truncation_stats = {
            'total_truncations': 0,
            'affected_rows': 0,
            'field_stats': {}
        }

        if field_lengths:
            logger.info("🔧 开始数据预处理，检查字段长度...")

            for col_name in df_clean.columns:
                if col_name in field_lengths:
                    max_length = field_lengths[col_name]
                    field_truncations = 0
                    affected_rows_for_field = 0

                    for idx in df_clean.index:
                        original_value = df_clean.at[idx, col_name]
                        if pd.notna(original_value):
                            original_str = str(original_value)
                            truncated_value = smart_truncate_text(original_str, max_length, col_name)

                            if len(str(truncated_value)) != len(original_str):
                                df_clean.at[idx, col_name] = truncated_value
                                field_truncations += 1
                                if affected_rows_for_field == 0:
                                    affected_rows_for_field = 1

                                logger.debug(f"✂️ 截断字段 {col_name} (行{idx+1}): {len(original_str)} -> {len(str(truncated_value))} 字符")

                    if field_truncations > 0:
                        truncation_stats['field_stats'][col_name] = {
                            'truncations': field_truncations,
                            'max_length': max_length
                        }
                        truncation_stats['total_truncations'] += field_truncations
                        truncation_stats['affected_rows'] += affected_rows_for_field
                        logger.info(f"📝 字段 {col_name}: 截断 {field_truncations} 次，最大长度限制 {max_length}")

            # 输出截断统计
            if truncation_stats['total_truncations'] > 0:
                logger.info(f"📊 数据预处理完成:")
                logger.info(f"  - 总截断次数: {truncation_stats['total_truncations']}")
                logger.info(f"  - 影响字段数: {len(truncation_stats['field_stats'])}")
                for field, stats in truncation_stats['field_stats'].items():
                    logger.info(f"  - {field}: {stats['truncations']} 次截断 (限制: {stats['max_length']} 字符)")
            else:
                logger.info("✅ 所有字段长度均符合要求，无需截断")

        # 先清空表数据
        clear_table_data(engine)

        # 批量导入数据
        batch_size = 50
        total_rows = len(df_clean)
        imported_rows = 0

        for i in range(0, total_rows, batch_size):
            batch_df = df_clean.iloc[i:i+batch_size]
            try:
                batch_df.to_sql(
                    TABLE_NAME,
                    engine,
                    if_exists='append',
                    index=False,
                    method='multi'
                )
                imported_rows += len(batch_df)
                logger.info(f"📊 已导入 {imported_rows}/{total_rows} 行数据 ({imported_rows/total_rows*100:.1f}%)")
            except Exception as e:
                logger.warning(f"⚠️ 批次导入失败，尝试逐行导入: {e}")
                # 如果批次导入失败，尝试逐行导入
                for _, row in batch_df.iterrows():
                    try:
                        row_df = pd.DataFrame([row])
                        row_df.to_sql(
                            TABLE_NAME,
                            engine,
                            if_exists='append',
                            index=False,
                            method='multi'
                        )
                        imported_rows += 1
                    except Exception as row_e:
                        logger.error(f"❌ 行导入失败: {row_e}")
                        continue
                logger.info(f"📊 已导入 {imported_rows}/{total_rows} 行数据 ({imported_rows/total_rows*100:.1f}%)")

        # 验证导入结果
        with engine.connect() as conn:
            result = conn.execute(text(f"SELECT COUNT(*) as count FROM {TABLE_NAME}"))
            count = result.fetchone()[0]
            logger.info(f"🔍 数据库中实际记录数: {count}")

        # 输出最终统计报告
        logger.info(f"✅ 数据导入完成！共导入 {imported_rows} 行数据")

        if truncation_stats['total_truncations'] > 0:
            logger.info(f"📊 字段截断统计报告:")
            logger.info(f"  🔢 总截断次数: {truncation_stats['total_truncations']}")
            logger.info(f"  📝 涉及字段数: {len(truncation_stats['field_stats'])}")
            logger.info(f"  📋 详细统计:")
            for field, stats in truncation_stats['field_stats'].items():
                logger.info(f"    - {field}: {stats['truncations']} 次截断 (字段限制: {stats['max_length']} 字符)")
            logger.info(f"  ✅ 所有数据已成功处理并导入，无数据丢失")
        else:
            logger.info(f"  ✅ 无字段长度问题，所有数据完整导入")

        return imported_rows

    except Exception as e:
        logger.error(f"❌ 数据导入失败: {e}")
        raise

sent_secIDs = {}  # 用于记录已发送价格下跌预警的股票

# 程序开始
logger.info("🚀 开始大笔买入股票分析程序")
logger.info(f"📅 分析日期: {predictDate}")

k = 120
while k > 0:
    ####大笔买入  symbol="大笔买入"; choice of {'火箭发射', '快速反弹', '大笔买入', '封涨停板', '打开跌停板', '有大买盘', '竞价上涨', '高开5日线', '向上缺口', '60日新高', '60日大幅上涨', '加速下跌', '高台跳水', '大笔卖出', '封跌停板', '打开涨停板', '有大卖盘', '竞价下跌', '低开5日线', '向下缺口', '60日新低', '60日大幅下跌'}
    stock_changes_em_df = ak.stock_changes_em(symbol="大笔买入")
    stock_changes_em_df['secID'] = stock_changes_em_df['代码'].astype(str).apply(lambda x: (x.zfill(6) + '.XSHE' if x.startswith(('00', '30')) or len(x) < 6 else (x.zfill(6) + '.XSHG' if x.startswith(('60', '68')) else x.zfill(6))))
    stock_changes_em_split = stock_changes_em_df['相关信息'].str.split(',', expand=True)
    # 检查实际列数并相应设置列名
    if stock_changes_em_split.shape[1] == 4:
        stock_changes_em_split.columns = ['Num', 'Price', 'chgPct', 'VolValue']
    else:
        stock_changes_em_split.columns = ['Num', 'Price', 'chgPct']
    stock_changes_em_df = pd.concat([stock_changes_em_df, stock_changes_em_split], axis=1)
    stock_changes_em_df['Num'] = pd.to_numeric(stock_changes_em_df['Num'], errors='coerce')
    stock_changes_em_df['Price'] = pd.to_numeric(stock_changes_em_df['Price'], errors='coerce')
    stock_changes_em_df['chgPct'] = pd.to_numeric(stock_changes_em_df['chgPct'], errors='coerce')
    stock_changes_em_df['chgPct'] = round(stock_changes_em_df['chgPct']*100, 2)
    stock_changes_em_df['buyin'] = round(stock_changes_em_df['Num'] * stock_changes_em_df['Price']/10000, 2)
    stock_changes_em_df.to_csv(f'{dir_path}/{predictDate}_{file_index:03d}.csv', index=False, encoding='utf-8-sig')
    stock_changes_em_df = stock_changes_em_df[(~stock_changes_em_df['名称'].str.contains(r'\*ST')) & (~stock_changes_em_df['名称'].str.contains('ST'))]
    print(f"大笔已保存到: {dir_path}")
    stock_changes_em_df_keep = stock_changes_em_df
    ### 大笔卖出
    stock_changes_em_df_maichu = ak.stock_changes_em(symbol="大笔卖出")
    stock_changes_em_split_maichu = stock_changes_em_df_maichu['相关信息'].str.split(',', expand=True)
    # 检查实际列数并相应设置列名
    if stock_changes_em_split_maichu.shape[1] == 4:
        stock_changes_em_split_maichu.columns = ['Num_Sell', 'Price_Sell', 'chgPct_Sell', 'Extra_Sell']
    else:
        stock_changes_em_split_maichu.columns = ['Num_Sell', 'Price_Sell', 'chgPct_Sell']
    stock_changes_em_df_maichu = pd.concat([stock_changes_em_df_maichu, stock_changes_em_split_maichu], axis=1)
    stock_changes_em_df_maichu['Num_Sell'] = pd.to_numeric(stock_changes_em_df_maichu['Num_Sell'], errors='coerce')
    stock_changes_em_df_maichu['Price_Sell'] = pd.to_numeric(stock_changes_em_df_maichu['Price_Sell'], errors='coerce')
    stock_changes_em_df_maichu['chgPct_Sell'] = pd.to_numeric(stock_changes_em_df_maichu['chgPct_Sell'], errors='coerce')
    stock_changes_em_df_maichu['chgPct_Sell'] = round(stock_changes_em_df_maichu['chgPct_Sell']*100, 2)
    stock_changes_em_df_maichu['Sellout'] = round(stock_changes_em_df_maichu['Num_Sell'] * stock_changes_em_df_maichu['Price_Sell']/10000, 2)
    stock_changes_em_df_maichu.drop(columns=['代码', '相关信息'], axis=1, inplace=True)
    ###  sum
    grouped_sum_maichu = stock_changes_em_df_maichu.groupby('名称')['Sellout'].sum()
    grouped_sum_df_maichu = grouped_sum_maichu.reset_index()
    stock_changes_em_df_maichu = stock_changes_em_df_maichu.merge(grouped_sum_df_maichu, on='名称', how='left')
    stock_changes_em_df_maichu = stock_changes_em_df_maichu.drop_duplicates(subset='名称', keep='first')
    stock_changes_em_df_maichu = stock_changes_em_df_maichu.sort_values(by='Sellout_y', ascending=False)
    stock_changes_em_df_maichu['Sellout_y'] = round(stock_changes_em_df_maichu['Sellout_y'], 2)
    stock_changes_em_df_maichu['Sellout_x'] = round(stock_changes_em_df_maichu['Sellout_x'], 2)
    stock_changes_em_df_maichu = stock_changes_em_df_maichu.rename(columns={'Sellout_y': '总卖出金额'})
    stock_changes_em_df_maichu = stock_changes_em_df_maichu.rename(columns={'Sellout_x': '现卖出金额'})
    ###  sum
    grouped_sum = stock_changes_em_df.groupby('名称')['buyin'].sum()
    # grouped_sum现在是一个Series，其索引是'名称'，值是对应的'buyin'求和结果
    # 如果你想要将这个求和结果作为一个新列添加到原始DataFrame中，可以使用reset_index
    grouped_sum_df = grouped_sum.reset_index()
    # 然后将这个结果合并回原始的DataFrame，只对有匹配'名称'的行进行更新
    stock_changes_em_df = stock_changes_em_df.merge(grouped_sum_df, on='名称', how='left')
    stock_changes_em_df['数量'] = stock_changes_em_df.groupby('名称').transform('size')
    stock_changes_em_df = stock_changes_em_df.drop_duplicates(subset='名称', keep='first')
    stock_changes_em_df = stock_changes_em_df.sort_values(by='buyin_y', ascending=False)
    stock_changes_em_df['buyin_y'] = round(stock_changes_em_df['buyin_y'], 2)
    stock_changes_em_df['buyin_x'] = round(stock_changes_em_df['buyin_x'], 2)
    stock_changes_em_df = stock_changes_em_df.rename(columns={'buyin_y': '总买入金额'})
    stock_changes_em_df = stock_changes_em_df.rename(columns={'buyin_x': '现买入金额'})
    # 合并版块信息
    stock_board_concept_name_ths_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_ths_merged.csv', encoding='utf-8-sig')
    stock_board_concept_name_ths_merged.drop(['secShortName'], axis=1, inplace=True)
    stock_board_concept_name_em_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_em_merged.csv', encoding='utf-8-sig')
    stock_board_concept_name_em_merged['secID'] = stock_board_concept_name_em_merged['代码'].astype(str).str.zfill(6).apply(lambda x: x + '.XSHE' if x.startswith(('00', '30')) else (x + '.XSHG' if x.startswith(('60', '68')) else x))
    stock_board_concept_name_em_merged.drop(['序号','代码','secShortName_x','最新价','涨跌幅', '涨跌额', '成交量', '成交额', '振幅', '最高', '最低', '今开', '昨收', '换手率', '市盈率-动态', '市净率'], axis=1, inplace=True)
    stock_changes_em_df=pd.merge(stock_changes_em_df, stock_board_concept_name_ths_merged, how='left', on=['secID'])
    stock_changes_em_df=pd.merge(stock_changes_em_df, stock_board_concept_name_em_merged, how='left', on=['secID'])
    ###
    stock_changes_em_df['总买入金额'].fillna(0, inplace=True)
    stock_changes_em_df_maichu['总卖出金额'].fillna(1, inplace=True)
    stock_changes_em_df = stock_changes_em_df.merge(stock_changes_em_df_maichu, on='名称', how='left')
    stock_changes_em_df['买卖比'] = round(stock_changes_em_df['总买入金额'] / stock_changes_em_df['总卖出金额'], 2)
    stock_changes_em_df = stock_changes_em_df.sort_values(by='买卖比', ascending=False)
    todaydate = (datetime.date.today() - datetime.timedelta(days=1)).strftime("%Y%m%d")
    path = os.path.exists(r'd:\Andy\Data\MktEqudGet\\'+todaydate+'.csv')
    while path != True:
        todaydate=(datetime.datetime.strptime(todaydate, '%Y%m%d')-datetime.timedelta(days = 1)).strftime('%Y%m%d')
        path = os.path.exists(r'd:\Andy\Data\MktEqudGet\\'+todaydate+'.csv')
    shizhi = pd.read_csv(r'd:\Andy\Data\MktEqudGet\\'+todaydate+'.csv', encoding='gbk')
    shizhi = shizhi[['secShortName','negMarketValue']]
    shizhi = shizhi.rename(columns={'secShortName': '名称'})
    stock_changes_em_df = stock_changes_em_df.merge(shizhi, on='名称', how='left')
    stock_changes_em_df['总买入占比'] = round(stock_changes_em_df['总买入金额']*10000 / stock_changes_em_df['negMarketValue'], 2)
    ## 加入融资融券信息
    rongzimingxi = pd.read_csv(r'd:\Andy\Data\融资融券明细\融资融券明细'+todaydate+'.csv')
    rongzimingxi = rongzimingxi.rename(columns={'secShortName': '名称'})
    rongzimingxi['融资买入额'] = round(rongzimingxi['融资买入额']/10000, 2)
    rongzimingxi['融资余额'] = round(rongzimingxi['融资余额']/10000, 2)
    stock_changes_em_df = stock_changes_em_df.merge(rongzimingxi, on='名称', how='left')
    ###

    ### 大笔买入
    N_shape_pool = pd.read_csv(r'd:\Andy\gupiao\N形待选_today.csv')
    stock_changes_em_df_dabi = stock_changes_em_df_keep.merge(N_shape_pool, on='secID', how='left')
    stock_changes_em_df_dabi.dropna(subset=['secShortName'], inplace=True)
    daemairu = stock_changes_em_df_dabi[(stock_changes_em_df_dabi['buyin'] > 800)]
    daemairu = daemairu[(daemairu['chgPct'] > -5) & (daemairu['chgPct'] < 8)]
    # 确保时间列是字符串类型
    daemairu['时间'] = daemairu['时间'].astype(str)
    daemairu = daemairu[daemairu['时间'] >= '09:31:00']
    # 新加一列，是同一个secID列'buyin'的值之和
    daemairu['buyin_value'] = daemairu.groupby('secID')['buyin'].transform('sum')
    stock_changes_em_df['today_sel'] = stock_changes_em_df['secID'].apply(lambda x: 1 if x in daemairu['secID'].values else 0)
    csv_file_path = r'd:\Andy\gupiao\大笔买入'+predictDate+'_sum.csv'
    stock_changes_em_df.to_csv(csv_file_path, index=False, encoding='utf-8-sig')
    logger.info(f"✅ CSV文件保存成功: {csv_file_path}")

    # 导入数据到MySQL数据库
    try:
        logger.info("🚀 开始导入大笔买入数据到MySQL数据库")
        engine = create_database_engine()
        imported_rows = import_csv_to_mysql(engine, csv_file_path)
        logger.info(f"🎉 数据库导入完成！共导入 {imported_rows} 行数据到 {TABLE_NAME} 表")
    except Exception as e:
        logger.error(f"💥 数据库导入失败: {e}")
        logger.info("⚠️ CSV文件已保存，但数据库导入失败，请检查数据库连接")
    finally:
        if 'engine' in locals():
            engine.dispose()
            logger.info("🔌 数据库连接已关闭")

    stock_changes_em_df_sel = stock_changes_em_df[['secID', '现买入金额', '总买入金额', '数量', '现卖出金额', '总卖出金额', '买卖比', '总买入占比']]
    daemairu = daemairu.merge(stock_changes_em_df_sel, on='secID', how='left')
    # 整理列的顺序并删减部分列
    daemairu = daemairu[['时间', 'secID', 'secShortName', '板块', 'Num', 'Price', 'chgPct', 'VolValue', 'buyin', '现买入金额', '总买入金额', '数量', '现卖出金额', '总卖出金额', '买卖比', '总买入占比', '题材', '题材汇总', '涨停原因', '题材（G）', 'tradeDate', 'ths概念名称', '板块名称', 'em板块名称']]
    # 按列'时间'倒序排列
    daemairu = daemairu.sort_values(by='时间', ascending=False)
    #####
    if len(daemairu)>0:
        # 保存符合条件的股票数据
        file_path_da = f'{dir_path2}/zhangting/founded_符合条件.csv'
        if not os.path.exists(file_path_da):
            # 如果文件不存在，直接保存
            daemairu.to_csv(file_path_da, index=False, encoding='utf-8-sig')
        else:
            # 如果文件存在，合并数据并去重
            existing_data_da = pd.read_csv(file_path_da)
            updated_data_da = pd.concat([existing_data_da, daemairu], ignore_index=True)
            # 数据清理和验证
            updated_data_da['时间'] = updated_data_da['时间'].astype(str).str.strip()
            updated_data_da['secID'] = updated_data_da['secID'].astype(str).str.strip()
            # 删除相同'时间'且相同'secID'的行
            updated_data_da = updated_data_da.drop_duplicates(subset=['secID', '时间'], keep='last')
            # 保存去重后的完整数据
            updated_data_da.to_csv(file_path_da, index=False, encoding='utf-8-sig')

    time.sleep(59)
    k -= 1
    file_index += 1
    current_time = datetime.datetime.now().time()
    # print(current_time)
    if current_time > datetime.time(15, 0):
        # 收盘后汇总当日符合条件的股票
        founded_sum = r'd:\Andy\gupiao\大笔买入'+predictDate+'_sum.csv'
        if os.path.exists(founded_sum):
            existing_sum = pd.read_csv(founded_sum)
            founded_qualified = f'{dir_path2}/zhangting/founded_符合条件.csv'
            if os.path.exists(founded_qualified):
                existing_data_da = pd.read_csv(founded_qualified)
                existing_sum2 = existing_sum[existing_sum['secID'].isin(existing_data_da['secID'])]
                if len(existing_sum2) > 0:
                    combined_string = '总买入金额_'+'数量_'+'买卖比_'+'总买入占比_'+'融资买入额\n'+'\n'.join(
                        existing_sum2['secID'].astype(str) + "_" +
                        existing_sum2['名称'] + "_" +
                        existing_sum2['总买入金额'].astype(str) + "_" +
                        existing_sum2['数量'].astype(str) + "_" +
                        existing_sum2['买卖比'].astype(str) + "_" +
                        existing_sum2['总买入占比'].astype(str) + "_" +
                        existing_sum2['融资买入额'].astype(str)
                    )
                    print(combined_string)
                    send_message("当日符合条件股票汇总", combined_string)
    if current_time > datetime.time(15, 0) or (current_time > datetime.time(11, 30) and current_time < datetime.time(13, 0)):
        break

logger.info("🎉 大笔买入股票分析程序执行完成！")
print("\n✅ 大笔买入股票分析和数据库导入完成！")
