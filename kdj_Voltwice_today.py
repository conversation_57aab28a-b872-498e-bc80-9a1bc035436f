import os
import pandas as pd
from datetime import datetime, timedelta
import requests
import os
import numpy as np

token = '44055711dfeb4124a8df9531f8dd2f59'  # 在pushplus网站中可以找到
def send_message(title, message):
    """发送消息到pushplus和ntfy.sh"""
    try:
        # requests.post("https://ntfy.sh/Xiaoertan", data=message.encode(encoding='utf-8'))
        url = f'http://www.pushplus.plus/send?token={token}&title={title}&content={message}'
        requests.get(url)
    except Exception as e:
        print("发送消息时发生错误:", e)

def load_data_efficiently(date):
    """只读取必要的列，减少内存使用"""
    needed_columns = {
        'equd': ['secID', 'secShortName', 'ticker', 'exchangeCD', 'chgPct', 'closePrice', 
                 'lowestPrice', 'highestPrice', 'preClosePrice', 'actPreClosePrice', 'turnoverVol', 'turnoverRate', 'openPrice', 'vwap'],
        'limit': ['secID', 'secShortName', 'limitUpPrice', 'limitDownPrice'],
        'factors': ['secID', 'MA5', 'MA10', 'MA20', 'MA60', 'EMA5', 'EMA10', 'EMA20', 'EMA60', 'RSI', 'KDJ_K', 'KDJ_D', 'KDJ_J', 'OBV', 'OBV6', 'OBV20', 'MACD', 'VEMA5', 'VEMA10', 'VEMA12', 'VEMA26', 'BollUp', 'BollDown', 'ATR6', 'ATR14', 'PSY', 'AR', 'BR', 'VR', 'BIAS5', 'BIAS10', 'BIAS20', 'BIAS60', 'ROC6', 'ROC20', 'EMV6', 'EMV14', 'MTM', 'MTMMA', 'PVT', 'PVT6', 'PVT12', 'TRIX5', 'TRIX10', 'MFI', 'WVAD', 'ChaikinOscillator', 'ChaikinVolatility', 'ASI', 'ARBR', 'CR20', 'ADTM', 'DDI', 'DEA', 'DIFF', 'BBI', 'TRIX5', 'TRIX10', 'UOS', 'MA10RegressCoeff12', 'MA10RegressCoeff6']
    }

    
    try:
        equd_df = pd.read_csv(os.path.join(equd_dir, f"{date}.csv"), 
                             encoding='gbk', usecols=needed_columns['equd'])
        limit_df = pd.read_csv(os.path.join(limit_dir, f"{date}.csv"), 
                              encoding='gbk', usecols=needed_columns['limit'])
        factors_df = pd.read_csv(os.path.join(mkt_stock_factors_dir, f"{date}.csv"), 
                                encoding='gbk', usecols=needed_columns['factors'])
        return pd.merge(pd.merge(equd_df, limit_df, on=["secID", "secShortName"]), 
                       factors_df, on=["secID"])
    except Exception as e:
        print(f"读取{date}数据出错: {e}")
        return None

def process_stock_data(group):
    """处理单个股票的数据"""
    if len(group) < 3:  # 至少需要3天数据
        return []
    
    results = []
    group = group.reset_index(drop=True)
    
    # print(f"\n处理股票: {group.iloc[0]['secShortName']} ({group.iloc[0]['secID']})")
    # print(f"数据天数: {len(group)}")
    
    found_patterns = 0
    for i in range(1, len(group)):  # 确保有足够的后续数据进行买卖
        # 检查KDJ三线是否在20-40区间内高度黏合
        k_value = group.loc[i, "KDJ_K"]
        d_value = group.loc[i, "KDJ_D"]
        j_value = group.loc[i, "KDJ_J"]
        
        # 检查是否有缺失值
        if pd.isna(k_value) or pd.isna(d_value) or pd.isna(j_value):
            continue
        
        # 检查是否都在小15区间内
        if k_value <= 15 and d_value <= 15 and j_value <= 15:
            # 计算三线极差
            kdj_values = [k_value, d_value, j_value]
            kdj_range = max(kdj_values) - min(kdj_values)
            # 检查当天成交量是否是前一天的两倍以上
            current_volume = group.loc[i, "turnoverVol"]
            prev_volume = group.loc[i-1, "turnoverVol"]
            
            # 确保成交量数据有效
            if pd.isna(current_volume) or pd.isna(prev_volume) or prev_volume == 0:
                continue
            
            volume_ratio = current_volume / prev_volume
            
            # 检查极差是否小于等于15
            if kdj_range <= 15 and volume_ratio >= 2:                    
                    result = {
                        "secID": group.iloc[0]["secID"],
                        "secShortName": group.iloc[0]["secShortName"],
                        "信号日期": group.loc[i, "date"],
                        "KDJ_K": k_value,
                        "KDJ_D": d_value,
                        "KDJ_J": j_value,
                        "KDJ极差": kdj_range,
                        "成交量比": volume_ratio
                    }
                    
                    # 添加第二天(i+1)相比第一天(i)的factors列比值
                    # factor_cols = ['MA5', 'MA10', 'MA20', 'MA60', 'EMA5', 'EMA10', 'EMA20', 'EMA60', 
                    #               'RSI', 'KDJ_K', 'KDJ_D', 'KDJ_J', 'OBV', 'OBV6', 'OBV20', 'MACD', 
                    #               'VEMA5', 'VEMA10', 'VEMA12', 'VEMA26', 'BollUp', 'BollDown', 'ATR6', 
                    #               'ATR14', 'PSY', 'AR', 'BR', 'VR', 'BIAS5', 'BIAS10', 'BIAS20', 'BIAS60', 
                    #               'ROC6', 'ROC20', 'EMV6', 'EMV14', 'MTM', 'MTMMA', 'PVT', 'PVT6', 'PVT12', 
                    #               'TRIX5', 'TRIX10', 'MFI', 'WVAD', 'ChaikinOscillator', 'ChaikinVolatility', 
                    #               'ASI', 'ARBR', 'CR20', 'ADTM', 'DDI', 'DEA', 'DIFF', 'BBI', 'UOS', 
                    #               'MA10RegressCoeff12', 'MA10RegressCoeff6']
                    factor_cols = ['BBI', 'EMA20', 'EMA10']

                    for col in factor_cols:
                        if col in group.columns:
                            current_value = group.loc[i, col]  # 第一天的值
                            prev_value = group.loc[i-1, col]       # 第0天的值
                            if prev_value != 0 and not pd.isna(current_value) and not pd.isna(prev_value):  # 避免除零错误和NaN
                                ratio = current_value / prev_value
                                result[f"i_ijian1_{col}_ratio"] = ratio
                    
                    found_patterns += 1
                    results.append(result)
    
    # print(f"找到符合条件的模式数量: {found_patterns}")
    return results

# 2. 然后是全局变量定义
token = '44055711dfeb4124a8df9531f8dd2f59'
equd_dir = "d:\\Andy\\Data\\MktEqudGet\\"
limit_dir = "d:\\Andy\\Data\\MktLimitGet\\"
mkt_stock_factors_dir = r'd:\Andy\Data\MktStockFactorsOneDayGet'

# 3. 主程序代码
end_date = (datetime.today() - timedelta(days=0)).strftime("%Y%m%d")
start_date = (datetime.strptime(end_date, '%Y%m%d') - timedelta(days=30)).strftime("%Y%m%d")
print("Start date:", start_date)
print("End date:", end_date)
output_file = 'd:\\Andy\\gupiao\\kdj_Voltwice_today.csv'

# 获取日期范围内的交易日
trading_dates = sorted([f.split('.')[0] for f in os.listdir(equd_dir) 
                      if f.endswith('.csv') and start_date <= f.split('.')[0] <= end_date])

print(f"找到交易日数量: {len(trading_dates)}")

# 创建一个空的DataFrame来存储所有日期的数据
all_data = pd.DataFrame()

# 读取所有日期的数据并合并
for date in trading_dates:
    daily_data = load_data_efficiently(date)
    if daily_data is not None:
        daily_data['date'] = date  # 添加日期列
        all_data = pd.concat([all_data, daily_data], ignore_index=True)

# 应用过滤条件
filtered_data = all_data[
    (all_data['ticker'] < 700000) & 
    (all_data['exchangeCD'].isin(["XSHE", "XSHG"])) & 
    (~all_data['secShortName'].str.contains('B|ST')) & 
    (all_data['closePrice'] > 3)
]

all_results = []
# 按股票代码分组，这样每个group就会包含该股票在整个日期范围内的所有数据
for secID, group in filtered_data.groupby("secID"):
    # 按日期排序
    group = group.sort_values('date')
    results = process_stock_data(group)
    all_results.extend(results)

print("\n最终结果统计:")
print(f"找到的总模式数量: {len(all_results)}")

results_df = pd.DataFrame(all_results)
if not results_df.empty:
    # 获取最大的反弹日期
    max_date = results_df['信号日期'].max()  
    results_df['i_ijian1_BBI_ratio'] = pd.to_numeric(results_df['i_ijian1_BBI_ratio'], errors='coerce').round(3)
    results_df['i_ijian1_EMA20_ratio'] = pd.to_numeric(results_df['i_ijian1_EMA20_ratio'], errors='coerce').round(3)
    results_df['i_ijian1_EMA10_ratio'] = pd.to_numeric(results_df['i_ijian1_EMA10_ratio'], errors='coerce').round(3)
    #将列“KDJ极差”保留小数点后两位
    results_df['KDJ极差'] = pd.to_numeric(results_df['KDJ极差'], 
    errors='coerce').round(2)
    #将列“KDJ_J”保留小数点后两位
    results_df['KDJ_J'] = pd.to_numeric(results_df['KDJ_J'], 
    errors='coerce').round(2)    
    # 只保留列“i1_反弹日期”的值最大的那些行 
    results_df = results_df[results_df['信号日期'] == max_date] 
    # 按照“KDJ_J”升序排序
    results_df = results_df.sort_values(by='i_ijian1_BBI_ratio', ascending=True)
    if len(results_df)>0:
        combined_string = '\n'.join(
            results_df['secID'].astype(str) + "_" +
            results_df['secShortName'].astype(str) + "_" +
            results_df['信号日期'].astype(str) + "_" +
            results_df['KDJ_J'].astype(str) + "_" +
            results_df['i_ijian1_BBI_ratio'].astype(str) + "_" +
            results_df['i_ijian1_EMA20_ratio'].astype(str) + "_" +
            results_df['i_ijian1_EMA10_ratio'].astype(str)
        )
        send_message("KDJ黏合并2倍成交量", combined_string)  
    # 保存结果
    results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n结果已保存到: {output_file}")