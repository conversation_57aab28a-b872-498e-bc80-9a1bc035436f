import akshare as ak
import datetime
import time
import os
import pandas as pd

import requests
import warnings
import boto3
from botocore.config import Config

warnings.filterwarnings('ignore')



# 设置保存路径和文件
token = '44055711dfeb4124a8df9531f8dd2f59'  # 在pushplus网站中可以找到
save_path = r'd:\Andy\gupiao\Data\Minutes_dabi'
predictDate = (datetime.date.today() - datetime.timedelta(days=0)).strftime("%Y%m%d")
dir_path = save_path + '/' + predictDate + '/'
dir_path2 = r'd:/Andy/gupiao/Data/Minutes' + '/' + predictDate + '/'

# Cloudflare R2配置
R2_ENDPOINT_URL = "https://5c38d853fbc5277d87a019147a8011a4.r2.cloudflarestorage.com"
R2_ACCESS_KEY_ID = "a10a932fcd224f965723f6779d70e3ca"
R2_SECRET_ACCESS_KEY = "14e2f52b70e2e3384596bcd8d34f719b3b0ce0addc0bd39d6400c0e943b23633"
R2_BUCKET_NAME = "dabimairu"

# 初始化S3客户端（用于R2）
s3_client = boto3.client(
    's3',
    endpoint_url=R2_ENDPOINT_URL,
    aws_access_key_id=R2_ACCESS_KEY_ID,
    aws_secret_access_key=R2_SECRET_ACCESS_KEY,
    config=Config(
        retries={'max_attempts': 3},
        connect_timeout=5,
        read_timeout=30
    )
)

def upload_to_r2(file_path, bucket_name=R2_BUCKET_NAME):
    """上传文件到Cloudflare R2存储桶"""
    try:
        # 获取原始文件名
        original_filename = os.path.basename(file_path)
        # 生成带时间戳的文件名（保留原始文件名，在后面添加时间戳）
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        # 分离文件名和扩展名
        filename_without_ext, file_extension = os.path.splitext(original_filename)
        file_name = f"{filename_without_ext}{file_extension}"  #_{timestamp}

        # print(f"开始上传文件: {file_name}")

        # 上传文件到R2
        with open(file_path, 'rb') as file:
            s3_client.upload_fileobj(
                file,
                bucket_name,
                file_name,
                ExtraArgs={
                    'ContentType': 'text/csv',
                    'ACL': 'private'
                }
            )

        # print(f"文件上传成功: {file_name}")
        # print(f"文件大小: {os.path.getsize(file_path)/1024:.2f} KB")
        return True
    except Exception as e:
        print(f"上传文件时出错: {str(e)}")
        return False

# 创建文件夹
if not os.path.exists(dir_path):
    os.makedirs(dir_path)
    os.makedirs(save_path + '/' + predictDate + '/zhangting/')

def count_csv_files(path):
    """统计指定目录下的CSV文件数量"""
    return sum(1 for filename in os.listdir(path) if filename.lower().endswith('.csv'))

file_index = count_csv_files(dir_path) + 1

def send_message(title, message):
    """发送消息到pushplus和ntfy.sh"""
    try:
        requests.post("https://ntfy.sh/Xiaoertan", data=message.encode(encoding='utf-8'))
        url = f'http://www.pushplus.plus/send?token={token}&title={title}&content={message}'
        requests.get(url)
    except Exception as e:
        print("发送消息时发生错误:", e)

sent_secIDs = {}  # 用于记录已发送价格下跌预警的股票
k = 120
while k > 0:
    ####大笔买入  symbol="大笔买入"; choice of {'火箭发射', '快速反弹', '大笔买入', '封涨停板', '打开跌停板', '有大买盘', '竞价上涨', '高开5日线', '向上缺口', '60日新高', '60日大幅上涨', '加速下跌', '高台跳水', '大笔卖出', '封跌停板', '打开涨停板', '有大卖盘', '竞价下跌', '低开5日线', '向下缺口', '60日新低', '60日大幅下跌'}
    stock_changes_em_df = ak.stock_changes_em(symbol="大笔买入")
    stock_changes_em_df['secID'] = stock_changes_em_df['代码'].astype(str).apply(lambda x: (x.zfill(6) + '.XSHE' if x.startswith(('00', '30')) or len(x) < 6 else (x.zfill(6) + '.XSHG' if x.startswith(('60', '68')) else x.zfill(6))))
    stock_changes_em_split = stock_changes_em_df['相关信息'].str.split(',', expand=True)
    # 检查实际列数并相应设置列名
    if stock_changes_em_split.shape[1] == 4:
        stock_changes_em_split.columns = ['Num', 'Price', 'chgPct', 'VolValue']
    else:
        stock_changes_em_split.columns = ['Num', 'Price', 'chgPct']
    stock_changes_em_df = pd.concat([stock_changes_em_df, stock_changes_em_split], axis=1)
    stock_changes_em_df['Num'] = pd.to_numeric(stock_changes_em_df['Num'], errors='coerce')
    stock_changes_em_df['Price'] = pd.to_numeric(stock_changes_em_df['Price'], errors='coerce')
    stock_changes_em_df['chgPct'] = pd.to_numeric(stock_changes_em_df['chgPct'], errors='coerce')
    stock_changes_em_df['chgPct'] = round(stock_changes_em_df['chgPct']*100, 2)
    stock_changes_em_df['buyin'] = round(stock_changes_em_df['Num'] * stock_changes_em_df['Price']/10000, 2)
    stock_changes_em_df.to_csv(f'{dir_path}/{predictDate}_{file_index:03d}.csv', index=False, encoding='utf-8-sig')
    stock_changes_em_df = stock_changes_em_df[(~stock_changes_em_df['名称'].str.contains(r'\*ST')) & (~stock_changes_em_df['名称'].str.contains('ST'))]
    print(f"大笔已保存到: {dir_path}")
    stock_changes_em_df_keep = stock_changes_em_df
    ### 大笔卖出
    stock_changes_em_df_maichu = ak.stock_changes_em(symbol="大笔卖出")
    stock_changes_em_split_maichu = stock_changes_em_df_maichu['相关信息'].str.split(',', expand=True)
    # 检查实际列数并相应设置列名
    if stock_changes_em_split_maichu.shape[1] == 4:
        stock_changes_em_split_maichu.columns = ['Num_Sell', 'Price_Sell', 'chgPct_Sell', 'Extra_Sell']
    else:
        stock_changes_em_split_maichu.columns = ['Num_Sell', 'Price_Sell', 'chgPct_Sell']
    stock_changes_em_df_maichu = pd.concat([stock_changes_em_df_maichu, stock_changes_em_split_maichu], axis=1)
    stock_changes_em_df_maichu['Num_Sell'] = pd.to_numeric(stock_changes_em_df_maichu['Num_Sell'], errors='coerce')
    stock_changes_em_df_maichu['Price_Sell'] = pd.to_numeric(stock_changes_em_df_maichu['Price_Sell'], errors='coerce')
    stock_changes_em_df_maichu['chgPct_Sell'] = pd.to_numeric(stock_changes_em_df_maichu['chgPct_Sell'], errors='coerce')
    stock_changes_em_df_maichu['chgPct_Sell'] = round(stock_changes_em_df_maichu['chgPct_Sell']*100, 2)
    stock_changes_em_df_maichu['Sellout'] = round(stock_changes_em_df_maichu['Num_Sell'] * stock_changes_em_df_maichu['Price_Sell']/10000, 2)
    stock_changes_em_df_maichu.drop(columns=['代码', '相关信息'], axis=1, inplace=True)
    ###  sum
    grouped_sum_maichu = stock_changes_em_df_maichu.groupby('名称')['Sellout'].sum()
    grouped_sum_df_maichu = grouped_sum_maichu.reset_index()
    stock_changes_em_df_maichu = stock_changes_em_df_maichu.merge(grouped_sum_df_maichu, on='名称', how='left')
    stock_changes_em_df_maichu = stock_changes_em_df_maichu.drop_duplicates(subset='名称', keep='first')
    stock_changes_em_df_maichu = stock_changes_em_df_maichu.sort_values(by='Sellout_y', ascending=False)
    stock_changes_em_df_maichu['Sellout_y'] = round(stock_changes_em_df_maichu['Sellout_y'], 2)
    stock_changes_em_df_maichu['Sellout_x'] = round(stock_changes_em_df_maichu['Sellout_x'], 2)
    stock_changes_em_df_maichu = stock_changes_em_df_maichu.rename(columns={'Sellout_y': '总卖出金额'})
    stock_changes_em_df_maichu = stock_changes_em_df_maichu.rename(columns={'Sellout_x': '现卖出金额'})
    ###  sum
    grouped_sum = stock_changes_em_df.groupby('名称')['buyin'].sum()
    # grouped_sum现在是一个Series，其索引是'名称'，值是对应的'buyin'求和结果
    # 如果你想要将这个求和结果作为一个新列添加到原始DataFrame中，可以使用reset_index
    grouped_sum_df = grouped_sum.reset_index()
    # 然后将这个结果合并回原始的DataFrame，只对有匹配'名称'的行进行更新
    stock_changes_em_df = stock_changes_em_df.merge(grouped_sum_df, on='名称', how='left')
    stock_changes_em_df['数量'] = stock_changes_em_df.groupby('名称').transform('size')
    stock_changes_em_df = stock_changes_em_df.drop_duplicates(subset='名称', keep='first')
    stock_changes_em_df = stock_changes_em_df.sort_values(by='buyin_y', ascending=False)
    stock_changes_em_df['buyin_y'] = round(stock_changes_em_df['buyin_y'], 2)
    stock_changes_em_df['buyin_x'] = round(stock_changes_em_df['buyin_x'], 2)
    stock_changes_em_df = stock_changes_em_df.rename(columns={'buyin_y': '总买入金额'})
    stock_changes_em_df = stock_changes_em_df.rename(columns={'buyin_x': '现买入金额'})
    # 合并版块信息
    stock_board_concept_name_ths_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_ths_merged.csv', encoding='utf-8-sig')
    stock_board_concept_name_ths_merged.drop(['secShortName'], axis=1, inplace=True)
    stock_board_concept_name_em_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_em_merged.csv', encoding='utf-8-sig')
    stock_board_concept_name_em_merged['secID'] = stock_board_concept_name_em_merged['代码'].astype(str).str.zfill(6).apply(lambda x: x + '.XSHE' if x.startswith(('00', '30')) else (x + '.XSHG' if x.startswith(('60', '68')) else x))
    stock_board_concept_name_em_merged.drop(['序号','代码','secShortName_x','最新价','涨跌幅', '涨跌额', '成交量', '成交额', '振幅', '最高', '最低', '今开', '昨收', '换手率', '市盈率-动态', '市净率'], axis=1, inplace=True)
    stock_changes_em_df=pd.merge(stock_changes_em_df, stock_board_concept_name_ths_merged, how='left', on=['secID'])
    stock_changes_em_df=pd.merge(stock_changes_em_df, stock_board_concept_name_em_merged, how='left', on=['secID'])


    # stock_board_concept_name_ths_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_ths_merged.csv', encoding='utf-8-sig')
    # stock_board_concept_name_ths_merged.drop(['序号','代码','现价','涨跌幅','振幅','成交额','涨跌','涨速', '换手', '量比', '流通股', '流通市值', '市盈率'], axis=1, inplace=True)
    # stock_board_concept_name_ths_merged = stock_board_concept_name_ths_merged.rename(columns={'secShortName_x': '名称'})
    # stock_board_concept_name_em_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_em_merged.csv', encoding='utf-8-sig')
    # stock_board_concept_name_em_merged.drop(['序号','代码','最新价','涨跌幅', '涨跌额', '成交量', '成交额', '振幅', '最高', '最低', '今开', '昨收', '换手率', '市盈率-动态', '市净率'], axis=1, inplace=True)
    # stock_board_concept_name_em_merged = stock_board_concept_name_em_merged.rename(columns={'secShortName_x': '名称'})
    # stock_changes_em_df=pd.merge(stock_changes_em_df, stock_board_concept_name_ths_merged, how='left', on=['名称'])
    # stock_changes_em_df=pd.merge(stock_changes_em_df, stock_board_concept_name_em_merged, how='left', on=['名称'])

    ###
    stock_changes_em_df['总买入金额'].fillna(0, inplace=True)
    stock_changes_em_df_maichu['总卖出金额'].fillna(1, inplace=True)
    stock_changes_em_df = stock_changes_em_df.merge(stock_changes_em_df_maichu, on='名称', how='left')
    stock_changes_em_df['买卖比'] = round(stock_changes_em_df['总买入金额'] / stock_changes_em_df['总卖出金额'], 2)
    stock_changes_em_df = stock_changes_em_df.sort_values(by='买卖比', ascending=False)
    todaydate = (datetime.date.today() - datetime.timedelta(days=1)).strftime("%Y%m%d")
    path = os.path.exists(r'd:\Andy\Data\MktEqudGet\\'+todaydate+'.csv')
    while path != True:
        todaydate=(datetime.datetime.strptime(todaydate, '%Y%m%d')-datetime.timedelta(days = 1)).strftime('%Y%m%d')
        path = os.path.exists(r'd:\Andy\Data\MktEqudGet\\'+todaydate+'.csv')
    shizhi = pd.read_csv(r'd:\Andy\Data\MktEqudGet\\'+todaydate+'.csv', encoding='gbk')
    shizhi = shizhi[['secShortName','negMarketValue']]
    shizhi = shizhi.rename(columns={'secShortName': '名称'})
    stock_changes_em_df = stock_changes_em_df.merge(shizhi, on='名称', how='left')
    stock_changes_em_df['总买入占比'] = round(stock_changes_em_df['总买入金额']*10000 / stock_changes_em_df['negMarketValue'], 2)
    ## 加入融资融券信息
    rongzimingxi = pd.read_csv(r'd:\Andy\Data\融资融券明细\融资融券明细'+todaydate+'.csv')
    rongzimingxi = rongzimingxi.rename(columns={'secShortName': '名称'})
    rongzimingxi['融资买入额'] = round(rongzimingxi['融资买入额']/10000, 2)
    rongzimingxi['融资余额'] = round(rongzimingxi['融资余额']/10000, 2)
    stock_changes_em_df = stock_changes_em_df.merge(rongzimingxi, on='名称', how='left')
    ###

    ### 大笔买入
    N_shape_pool = pd.read_csv(r'd:\Andy\gupiao\N形待选_today.csv')
    stock_changes_em_df_dabi = stock_changes_em_df_keep.merge(N_shape_pool, on='secID', how='left')
    stock_changes_em_df_dabi.dropna(subset=['secShortName'], inplace=True)
    daemairu = stock_changes_em_df_dabi[(stock_changes_em_df_dabi['buyin'] > 800)]
    daemairu = daemairu[(daemairu['chgPct'] > -5) & (daemairu['chgPct'] < 8)]
    # 确保时间列是字符串类型
    daemairu['时间'] = daemairu['时间'].astype(str)
    daemairu = daemairu[daemairu['时间'] >= '09:31:00']
    # 新加一列，是同一个secID列'buyin'的值之和
    daemairu['buyin_value'] = daemairu.groupby('secID')['buyin'].transform('sum')
    # 只留下'buyin_value'大于2000的行
    # daemairu = daemairu[daemairu['buyin_value'] > 2000]
    # 保存去重前的完整数据
    # daemairu.to_csv(f'{dir_path2}/zhangting/founded_N型全.csv', index=False, encoding='utf-8-sig')
    # daemairu = daemairu.drop_duplicates(subset='secID', keep='last')
    # stock_changes_em_df_sel = stock_changes_em_df[['secID', '现买入金额', '总买入金额', '数量', '现卖出金额', '总卖出金额', '买卖比', '总买入占比']]
    # daemairu = daemairu.merge(stock_changes_em_df_sel, on='secID', how='left')
    # daemairu = daemairu[(daemairu['数量'] > 1)]
    # daemairu = daemairu.sort_values(by='buyin', ascending=True)
    # dfo = ak.stock_zh_a_spot_em()
    # dfo['secID'] = dfo['代码'].astype(str).apply(lambda x: x + '.XSHE' if x.startswith(('00', '30')) else (x + '.XSHG' if x.startswith(('60', '68')) else x))
    # dfo = dfo[['secID','涨跌幅','换手率']]
    # daemairu = daemairu.merge(dfo, on='secID', how='left')
    ###

    ### 在stock_changes_em_df中新加一列"today_sel"，当secID在daemairu中出现时为1，否则为0
    stock_changes_em_df['today_sel'] = stock_changes_em_df['secID'].apply(lambda x: 1 if x in daemairu['secID'].values else 0)
    csv_file_path = r'd:\Andy\gupiao\大笔买入'+predictDate+'_sum.csv'
    stock_changes_em_df.to_csv(csv_file_path, index=False, encoding='utf-8-sig')

    # 将这个csv文件上传到cloudflare的R2 存储桶
    # upload_result = upload_to_r2(csv_file_path)
    # if upload_result:
    #     print(f"成功将文件 {csv_file_path} 上传到Cloudflare R2存储桶")
    # else:
    #     print(f"上传文件 {csv_file_path} 到Cloudflare R2存储桶失败")

    stock_changes_em_df_sel = stock_changes_em_df[['secID', '现买入金额', '总买入金额', '数量', '现卖出金额', '总卖出金额', '买卖比', '总买入占比']]
    daemairu = daemairu.merge(stock_changes_em_df_sel, on='secID', how='left')
    # 整理列的顺序并删减部分列
    daemairu = daemairu[['时间', 'secID', 'secShortName', '板块', 'Num', 'Price', 'chgPct', 'VolValue', 'buyin', '现买入金额', '总买入金额', '数量', '现卖出金额', '总卖出金额', '买卖比', '总买入占比', '题材', '题材汇总', '涨停原因', '题材（G）', 'tradeDate', 'ths概念名称', '板块名称', 'em板块名称']]
    # 按列'时间'倒序排列
    daemairu = daemairu.sort_values(by='时间', ascending=False)
    #####
    if len(daemairu)>0:
        # 保存符合条件的股票数据
        file_path_da = f'{dir_path2}/zhangting/founded_符合条件.csv'
        if not os.path.exists(file_path_da):
            # 如果文件不存在，直接保存
            daemairu.to_csv(file_path_da, index=False, encoding='utf-8-sig')
        else:
            # 如果文件存在，合并数据并去重
            existing_data_da = pd.read_csv(file_path_da)
            updated_data_da = pd.concat([existing_data_da, daemairu], ignore_index=True)
            # 数据清理和验证
            updated_data_da['时间'] = updated_data_da['时间'].astype(str).str.strip()
            updated_data_da['secID'] = updated_data_da['secID'].astype(str).str.strip()
            # 删除相同'时间'且相同'secID'的行
            updated_data_da = updated_data_da.drop_duplicates(subset=['secID', '时间'], keep='last')
            # 保存去重后的完整数据
            updated_data_da.to_csv(file_path_da, index=False, encoding='utf-8-sig')
        # 价格下跌预警检测
        reminder_records = []

        # 检查当前符合条件股票的价格变化
        if len(daemairu) > 0:
            # 获取最新的分钟数据进行价格下跌检测
            try:
                # 获取当前最新的股票数据
                # current_stock_data = ak.stock_zh_a_spot()
                # current_stock_data['secID'] = current_stock_data['代码'].astype(str).apply(
                    # lambda x: x + '.XSHE' if x.startswith(('00', '30')) else (x + '.XSHG' if x.startswith(('60', '68')) else x)
                # )
                # current_stock_data['secID'] = current_stock_data['代码'].astype(str).apply(lambda x: x[2:] + '.XSHE' if x.startswith(('sz')) else (x[2:] + '.XSHG' if x.startswith(('sh')) else x))

                # 只关注符合条件的股票
                # monitored_stocks = current_stock_data[current_stock_data['secID'].isin(daemairu['secID'])]
                # print(len(daemairu))

                # 将daemairu按照secID删除重复项
                daemairu = daemairu.drop_duplicates(subset=['secID'], keep='last')
                # print(len(daemairu))

                # 将daemairu中secID的值转换，规则是：如果是'.XSHE'结尾，则去掉结尾的'.XSHE'，并在开头加上SZ，如果是'.XSHG'结尾，则去掉结尾的'.XSHG'，并在开头加上SH
                daemairu['secIDB'] = daemairu['secID'].astype(str).apply(lambda x: 'SZ' + x[:-5] if x.endswith(('.XSHE')) else ('SH' + x[:-5] if x.endswith(('.XSHG')) else x))

                monitored_stocks = pd.DataFrame()

                # 从daemairu中一行一行的获取secIDB
                for index, row in daemairu.iterrows():
                    secIDB = row['secIDB']
                    # print(secIDB)
                    stock_individual_spot_xq_df = ak.stock_individual_spot_xq(symbol=secIDB)
                    # print(stock_individual_spot_xq_df)
                    # 转换DataFrame格式：将item列的值变成列名，value列的值变成对应的值
                    if not stock_individual_spot_xq_df.empty:
                        # 检查是否包含'item'和'value'列
                        if 'item' in stock_individual_spot_xq_df.columns and 'value' in stock_individual_spot_xq_df.columns:
                            # 将item列的值作为新的列名，value列的值作为对应的值
                            stock_individual_spot_xq_transformed = pd.DataFrame([stock_individual_spot_xq_df.set_index('item')['value']])
                            # 将转换后的DataFrame添加到monitored_stocks
                            monitored_stocks = pd.concat([monitored_stocks, stock_individual_spot_xq_transformed], ignore_index=True)
                        else:
                            # 如果没有item和value列，则直接添加原始数据
                            monitored_stocks = pd.concat([monitored_stocks, stock_individual_spot_xq_df], ignore_index=True)
                    # print(stock_individual_spot_xq_df)

                # 找出价格跌至最低价的股票
                if len(monitored_stocks) > 0:
                    monitored_stocks['secID'] = monitored_stocks['代码'].astype(str).apply(lambda x: x[2:] + '.XSHE' if x.startswith(('SZ')) else (x[2:] + '.XSHG' if x.startswith(('SH')) else x))
                    monitored_stocks.to_csv(f'{dir_path2}/zhangting/monitored_stocks.csv', index=False, encoding='utf-8-sig')
                    price_drop_stocks = monitored_stocks[monitored_stocks['现价'] <= monitored_stocks['最低']]

                # 筛选出未发送过预警的股票
                new_price_drop_stocks = price_drop_stocks[~price_drop_stocks['secID'].isin(sent_secIDs)]

                if len(new_price_drop_stocks) > 0:
                    new_price_drop_stocks.to_csv(f'{dir_path2}/zhangting/预警_{file_index}.csv', index=False, encoding='utf-8-sig')
                    # 发送价格下跌预警
                    combined_string = '股票代码_名称_最新价_最低价_涨跌幅\n' + '\n'.join(
                        new_price_drop_stocks['secID'].astype(str) + "_" +
                        new_price_drop_stocks['名称'] + "_" +
                        new_price_drop_stocks['现价'].astype(str) + "_" +
                        new_price_drop_stocks['最低'].astype(str) + "_" +
                        new_price_drop_stocks['涨幅'].astype(str)
                    )
                    send_message("价格下跌预警", combined_string)

                    # 更新已发送记录
                    for secID in new_price_drop_stocks['secID']:
                        sent_secIDs[secID] = True

                    # 记录提醒信息
                    for _, row in new_price_drop_stocks.iterrows():
                        reminder_info = row.to_dict()
                        reminder_info['提醒时间'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        reminder_info['提醒类型'] = '价格下跌预警'
                        reminder_records.append(reminder_info)

            except Exception as e:
                print(f"价格下跌检测时出错: {e}")



        # 保存价格下跌提醒信息
        if len(reminder_records) > 0:
            reminder_df = pd.DataFrame(reminder_records)
            reminder_df = reminder_df.drop_duplicates(subset=['secID'], keep='first')
            reminder_df.to_csv(f'{dir_path2}/zhangting/价格下跌提醒_{predictDate}.csv', index=False, encoding='utf-8-sig')
            print(f"生成{len(reminder_df)}条价格下跌提醒记录")

    time.sleep(59)
    k -= 1
    file_index += 1
    current_time = datetime.datetime.now().time()
    # print(current_time)
    if current_time > datetime.time(15, 0):
        # 收盘后汇总当日符合条件的股票
        founded_sum = r'd:\Andy\gupiao\大笔买入'+predictDate+'_sum.csv'
        if os.path.exists(founded_sum):
            existing_sum = pd.read_csv(founded_sum)
            founded_qualified = f'{dir_path2}/zhangting/founded_符合条件.csv'
            if os.path.exists(founded_qualified):
                existing_data_da = pd.read_csv(founded_qualified)
                existing_sum2 = existing_sum[existing_sum['secID'].isin(existing_data_da['secID'])]
                if len(existing_sum2) > 0:
                    combined_string = '总买入金额_'+'数量_'+'买卖比_'+'总买入占比_'+'融资买入额\n'+'\n'.join(
                        existing_sum2['secID'].astype(str) + "_" +
                        existing_sum2['名称'] + "_" +
                        existing_sum2['总买入金额'].astype(str) + "_" +
                        existing_sum2['数量'].astype(str) + "_" +
                        existing_sum2['买卖比'].astype(str) + "_" +
                        existing_sum2['总买入占比'].astype(str) + "_" +
                        existing_sum2['融资买入额'].astype(str)
                    )
                    print(combined_string)
                    send_message("当日符合条件股票汇总", combined_string)
    if current_time > datetime.time(15, 0) or (current_time > datetime.time(11, 30) and current_time < datetime.time(13, 0)):
        break
