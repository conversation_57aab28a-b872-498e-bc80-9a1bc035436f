#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查n_shape_candidates表结构和字段长度限制
"""

import sys
import logging
import pandas as pd
from datetime import datetime
from sqlalchemy import create_engine, text

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': 'stock-31bk-stock-31bankuai.c.aivencloud.com',
    'port': 26991,
    'user': 'avnadmin',
    'password': 'AVNS_daq_tCJ6LP2VwbS_633',
    'database': 'defaultdb',
    'charset': 'utf8mb4'
}

TABLE_NAME = 'n_shape_candidates'

def check_table_structure():
    """检查表结构和字段长度限制"""
    try:
        connection_url = (
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}"
            f"@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['database']}"
            f"?charset={MYSQL_CONFIG['charset']}&ssl_verify_cert=false&ssl_verify_identity=false"
        )
        
        engine = create_engine(connection_url, echo=False)
        
        with engine.connect() as conn:
            # 检查表是否存在
            result = conn.execute(text(f"SHOW TABLES LIKE '{TABLE_NAME}'"))
            if not result.fetchone():
                logger.error(f"❌ 表 {TABLE_NAME} 不存在")
                return None
            
            logger.info(f"✅ 表 {TABLE_NAME} 存在")
            
            # 获取表结构
            result = conn.execute(text(f"DESCRIBE {TABLE_NAME}"))
            columns = result.fetchall()
            
            logger.info(f"📋 表结构详情:")
            field_info = {}
            
            for col in columns:
                field_name = col[0]
                field_type = col[1]
                is_null = col[2]
                key = col[3]
                default = col[4]
                extra = col[5]
                
                # 解析字段长度
                max_length = None
                if 'varchar' in field_type.lower():
                    # 提取varchar长度
                    start = field_type.find('(')
                    end = field_type.find(')')
                    if start != -1 and end != -1:
                        max_length = int(field_type[start+1:end])
                elif 'text' in field_type.lower():
                    if 'longtext' in field_type.lower():
                        max_length = 4294967295  # LONGTEXT最大长度
                    elif 'mediumtext' in field_type.lower():
                        max_length = 16777215    # MEDIUMTEXT最大长度
                    else:
                        max_length = 65535       # TEXT最大长度
                
                field_info[field_name] = {
                    'type': field_type,
                    'max_length': max_length,
                    'is_null': is_null,
                    'key': key,
                    'default': default,
                    'extra': extra
                }
                
                logger.info(f"  - {field_name}: {field_type} (最大长度: {max_length})")
            
            # 检查当前数据中的字段长度
            logger.info(f"\n🔍 检查当前数据中的字段长度:")
            result = conn.execute(text(f"SELECT COUNT(*) FROM {TABLE_NAME}"))
            total_rows = result.fetchone()[0]
            logger.info(f"📊 当前表中共有 {total_rows} 行数据")
            
            if total_rows > 0:
                # 检查文本字段的实际长度
                text_fields = [name for name, info in field_info.items() 
                              if info['max_length'] and 'varchar' in info['type'].lower()]
                
                for field in text_fields:
                    try:
                        result = conn.execute(text(f"SELECT MAX(CHAR_LENGTH(`{field}`)) as max_len FROM {TABLE_NAME} WHERE `{field}` IS NOT NULL"))
                        max_actual_length = result.fetchone()[0] or 0
                        max_allowed = field_info[field]['max_length']
                        
                        status = "✅" if max_actual_length <= max_allowed else "❌"
                        logger.info(f"  {status} {field}: 实际最大长度 {max_actual_length}, 允许最大长度 {max_allowed}")
                        
                        if max_actual_length > max_allowed:
                            # 查找超长的记录
                            result = conn.execute(text(f"SELECT secID, secShortName, CHAR_LENGTH(`{field}`) as len FROM {TABLE_NAME} WHERE CHAR_LENGTH(`{field}`) > {max_allowed} LIMIT 5"))
                            over_length_records = result.fetchall()
                            logger.warning(f"    超长记录示例:")
                            for record in over_length_records:
                                logger.warning(f"      {record[0]} ({record[1]}): 长度 {record[2]}")
                    except Exception as e:
                        logger.warning(f"  ⚠️ 检查字段 {field} 时出错: {e}")
            
            return field_info
                
    except Exception as e:
        logger.error(f"❌ 检查表结构失败: {e}")
        return None
    finally:
        if 'engine' in locals():
            engine.dispose()

if __name__ == "__main__":
    logger.info("🚀 开始检查表结构")
    field_info = check_table_structure()
    if field_info:
        print("\n✅ 表结构检查完成！")
    else:
        print("\n❌ 表结构检查失败！")
