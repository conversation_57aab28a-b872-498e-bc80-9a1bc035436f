#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试tupo_dabimairu.py的数据库导入功能
"""

import os
import sys
import logging
import pandas as pd
from datetime import datetime
from sqlalchemy import create_engine, text

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': 'stock-31bk-stock-31bankuai.c.aivencloud.com',
    'port': 26991,
    'user': 'avnadmin',
    'password': 'AVNS_daq_tCJ6LP2VwbS_633',
    'database': 'defaultdb',
    'charset': 'utf8mb4'
}

TABLE_NAME = 'large_buy_orders'

def test_database_connection():
    """测试数据库连接"""
    try:
        connection_url = (
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}"
            f"@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['database']}"
            f"?charset={MYSQL_CONFIG['charset']}&ssl_verify_cert=false&ssl_verify_identity=false"
        )
        
        engine = create_engine(connection_url, echo=False)
        
        with engine.connect() as conn:
            # 测试连接
            result = conn.execute(text("SELECT 1"))
            if result.fetchone():
                logger.info("✅ 数据库连接成功")
                
                # 检查表是否存在
                result = conn.execute(text(f"SHOW TABLES LIKE '{TABLE_NAME}'"))
                if result.fetchone():
                    logger.info(f"✅ 表 {TABLE_NAME} 存在")
                    
                    # 查看表结构
                    result = conn.execute(text(f"DESCRIBE {TABLE_NAME}"))
                    columns = result.fetchall()
                    logger.info(f"📋 表结构:")
                    for col in columns:
                        logger.info(f"  - {col[0]}: {col[1]}")
                    
                    # 查看记录数
                    result = conn.execute(text(f"SELECT COUNT(*) FROM {TABLE_NAME}"))
                    count = result.fetchone()[0]
                    logger.info(f"📊 当前记录数: {count}")
                else:
                    logger.warning(f"⚠️ 表 {TABLE_NAME} 不存在")
                
                return True
            else:
                logger.error("❌ 数据库连接测试失败")
                return False
                
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        return False
    finally:
        if 'engine' in locals():
            engine.dispose()

def create_test_csv():
    """创建测试CSV文件"""
    test_data = {
        '时间': ['09:30:00', '09:31:00', '09:32:00'],
        '代码': ['000001', '000002', '600000'],
        '名称': ['平安银行', '万科A', '浦发银行'],
        '相关信息': ['1000,10.5,2.5,1050', '800,8.2,-1.2,656', '1200,12.3,3.8,1476'],
        'secID': ['000001.XSHE', '000002.XSHE', '600000.XSHG'],
        'Num': [1000, 800, 1200],
        'Price': [10.5, 8.2, 12.3],
        'chgPct': [2.5, -1.2, 3.8],
        'VolValue': [1050, 656, 1476],
        'buyin': [105.0, 65.6, 147.6],
        '总买入金额': [105.0, 65.6, 147.6],
        '现买入金额': [105.0, 65.6, 147.6],
        '数量': [1, 1, 1],
        '现卖出金额': [50.0, 30.0, 70.0],
        '总卖出金额': [50.0, 30.0, 70.0],
        '买卖比': [2.1, 2.19, 2.11],
        'negMarketValue': [500000, 300000, 700000],
        '总买入占比': [0.21, 0.22, 0.21],
        '融资买入额': [20.0, 15.0, 25.0],
        '融资余额': [200.0, 150.0, 250.0],
        'today_sel': [1, 0, 1],
        # 添加一些可能超长的字段用于测试
        'ths概念名称': [
            '银行 金融科技 数字货币 区块链 人工智能 大数据 云计算',
            '房地产 城市更新 租售同权 保障房 物业管理',
            '银行 金融科技 数字货币 区块链 人工智能 大数据 云计算 互联网金融'
        ],
        'em板块名称': [
            '银行板块,金融科技板块,数字货币板块,区块链板块',
            '房地产板块,城市更新板块,租售同权板块',
            '银行板块,金融科技板块,数字货币板块,区块链板块,互联网金融板块'
        ]
    }
    
    df = pd.DataFrame(test_data)
    test_csv_path = r'd:\Andy\gupiao\test_大笔买入_sum.csv'
    df.to_csv(test_csv_path, index=False, encoding='utf-8-sig')
    logger.info(f"✅ 测试CSV文件创建成功: {test_csv_path}")
    
    # 显示字段长度信息
    for col in df.columns:
        max_len = df[col].astype(str).str.len().max()
        logger.info(f"📏 字段 {col} 最大长度: {max_len} 字符")
    
    return test_csv_path

def test_import_functions():
    """测试导入函数"""
    try:
        # 导入修改后的函数
        sys.path.append('d:\\Andy\\coding\\gupiao_huice')
        
        # 测试数据库连接
        if not test_database_connection():
            logger.error("❌ 数据库连接测试失败")
            return False
        
        # 创建测试CSV
        test_csv_path = create_test_csv()
        
        logger.info("✅ 大笔买入数据库导入功能测试准备完成")
        logger.info(f"📁 测试CSV文件: {test_csv_path}")
        logger.info(f"🗄️ 目标数据库表: {TABLE_NAME}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 开始测试大笔买入数据库导入功能")
    
    if test_import_functions():
        print("\n✅ 大笔买入数据库导入功能测试通过！")
        print("💡 提示：可以运行修改后的 tupo_dabimairu.py 来测试完整的导入流程")
    else:
        print("\n❌ 大笔买入数据库导入功能测试失败！")
