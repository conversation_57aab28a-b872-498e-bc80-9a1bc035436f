#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单测试修改后的代码功能
"""

import os
import sys
import logging
import pandas as pd
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

def test_csv_creation():
    """测试CSV文件创建功能"""
    try:
        # 创建测试数据
        test_data = {
            'secID': ['000001.XSHE', '000002.XSHE', '600000.XSHG'],
            'secShortName': ['平安银行', '万科A', '浦发银行'],
            '涨停日期': ['20250101', '20250102', '20250103'],
            '缩量下跌天数': [3, 2, 4],
            '持续缩量': ['持续缩量', '否', '持续缩量'],
            '最后一天收盘价': [10.5, 8.2, 12.3],
            '最低价格位置': ['5日线上', '10日线以下', '5日线以下'],
            '收盘价格位置': ['5日线上', '5日线以下', '10日线以下']
        }
        
        df = pd.DataFrame(test_data)
        
        # 保存测试CSV文件
        test_csv_path = 'd:\\Andy\\gupiao\\test_N形待选_today.csv'
        df.to_csv(test_csv_path, index=False, encoding='utf-8-sig')
        logger.info(f"✅ 测试CSV文件创建成功: {test_csv_path}")
        
        # 验证文件存在
        if os.path.exists(test_csv_path):
            logger.info(f"✅ 文件验证成功，文件大小: {os.path.getsize(test_csv_path)} 字节")
            
            # 读取验证
            df_read = pd.read_csv(test_csv_path, encoding='utf-8-sig')
            logger.info(f"✅ 文件读取成功，行数: {len(df_read)}, 列数: {len(df_read.columns)}")
            logger.info(f"📋 列名: {list(df_read.columns)}")
            
            return test_csv_path
        else:
            logger.error("❌ 文件创建失败")
            return None
            
    except Exception as e:
        logger.error(f"❌ 测试CSV创建失败: {e}")
        return None

def test_import_functions():
    """测试导入的数据库函数"""
    try:
        # 导入修改后的文件中的函数
        sys.path.append('d:\\Andy\\coding\\gupiao_huice')
        
        # 测试导入
        from sqlalchemy import create_engine, text
        logger.info("✅ SQLAlchemy导入成功")
        
        import pymysql
        logger.info("✅ PyMySQL导入成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 导入测试失败: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 开始简单功能测试")
    
    # 测试导入
    if test_import_functions():
        logger.info("✅ 导入测试通过")
    else:
        logger.error("❌ 导入测试失败")
    
    # 测试CSV创建
    csv_path = test_csv_creation()
    if csv_path:
        logger.info("✅ CSV测试通过")
    else:
        logger.error("❌ CSV测试失败")
    
    print("\n✅ 简单功能测试完成！")
