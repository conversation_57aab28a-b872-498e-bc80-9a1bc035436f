#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分析涨停数据文件，统计收益率列的不同阈值下的正确率和均值，
以及在"涨停当天_ROC10"不同阶梯时的统计结果
"""

import pandas as pd
import numpy as np
from pathlib import Path

# 文件路径
input_file = 'zhangting_20241015_20250710.csv'
output_file_csv = 'zhangting_统计结果.csv'
output_file_excel = 'zhangting_统计结果.xlsx'

# 读取CSV文件
print(f"正在读取文件: {input_file}")
df = pd.read_csv(input_file)

# 显示数据基本信息
print(f"数据行数: {len(df)}")
print(f"数据列数: {df.shape[1]}")
print("数据列名:", df.columns.tolist())

# 确保关键列存在
required_columns = ['收益率', '涨停当天_ROC10']
for col in required_columns:
    if col not in df.columns:
        raise ValueError(f"数据中缺少必要的列: {col}")

# 显示数据前几行
print("\n数据前5行:")
print(df[required_columns + ['secID', 'secShortName']].head())

# 计算收益率列的基本统计信息
print("\n计算收益率列的基本统计信息...")

# 总样本数
total_samples = len(df)
print(f"总样本数: {total_samples}")

# 定义收益率阈值
thresholds = [0, 1, 3]

# 创建一个字典来存储结果
yield_stats = {}

# 计算每个阈值的正确率和均值
for threshold in thresholds:
    # 大于阈值的样本
    above_threshold = df[df['收益率'] > threshold]
    # 正确率 = 大于阈值的样本数 / 总样本数
    accuracy = len(above_threshold) / total_samples * 100
    # 大于阈值的样本的均值
    mean_yield = above_threshold['收益率'].mean()
    
    # 存储结果
    yield_stats[f"收益率 > {threshold}"] = {
        "样本数": len(above_threshold),
        "正确率(%)": accuracy,
        "均值(%)": mean_yield
    }
    
    print(f"收益率 > {threshold}: 样本数={len(above_threshold)}, 正确率={accuracy:.2f}%, 均值={mean_yield:.2f}%")

# 将结果转换为DataFrame
yield_stats_df = pd.DataFrame(yield_stats).T

# 定义ROC10的阶梯区间
print("\n定义ROC10的阶梯区间...")

# 定义区间边界
roc10_bins = [-float('inf'), 0, 10, 30, 50, 70, 90, float('inf')]
# 定义区间标签
roc10_labels = ['ROC10 < 0', 'ROC10 > 0', 'ROC10 > 10', 'ROC10 > 30', 'ROC10 > 50', 'ROC10 > 70', 'ROC10 > 90']

# 根据ROC10的值将数据分组
df['ROC10_区间'] = pd.cut(df['涨停当天_ROC10'], bins=roc10_bins, labels=roc10_labels)

# 显示每个区间的样本数
print("\n各ROC10区间的样本数:")
roc10_counts = df['ROC10_区间'].value_counts().sort_index()
print(roc10_counts)

# 创建一个字典来存储不同ROC10区间的收益率统计信息
roc10_yield_stats = {}

# 对每个ROC10区间，计算收益率的统计信息
for interval in roc10_labels:
    # 获取该区间的数据
    interval_data = df[df['ROC10_区间'] == interval]
    
    # 该区间的样本数
    interval_samples = len(interval_data)
    
    # 如果该区间没有样本，跳过
    if interval_samples == 0:
        continue
    
    # 创建该区间的统计信息字典
    interval_stats = {
        "样本数": interval_samples,
        "样本占比(%)": interval_samples / total_samples * 100
    }
    
    # 对每个收益率阈值，计算正确率和均值
    for threshold in thresholds:
        # 大于阈值的样本
        above_threshold = interval_data[interval_data['收益率'] > threshold]
        # 正确率 = 大于阈值的样本数 / 该区间的样本数
        accuracy = len(above_threshold) / interval_samples * 100 if interval_samples > 0 else 0
        # 大于阈值的样本的均值
        mean_yield = above_threshold['收益率'].mean() if len(above_threshold) > 0 else 0
        
        # 存储结果
        interval_stats[f"收益率 > {threshold} 正确率(%)"] = accuracy
        interval_stats[f"收益率 > {threshold} 均值(%)"] = mean_yield
    
    # 存储该区间的统计信息
    roc10_yield_stats[interval] = interval_stats
    
    print(f"\n{interval} 区间统计:")
    print(f"  样本数: {interval_samples}")
    print(f"  样本占比: {interval_stats['样本占比(%)']: .2f}%")
    for threshold in thresholds:
        print(f"  收益率 > {threshold}: 正确率={interval_stats[f'收益率 > {threshold} 正确率(%)']: .2f}%, "
              f"均值={interval_stats[f'收益率 > {threshold} 均值(%)']: .2f}%")

# 将结果转换为DataFrame
roc10_yield_stats_df = pd.DataFrame(roc10_yield_stats).T

# 创建最终结果表格
print("\n创建最终结果表格...")

# 创建一个多级列索引的结果表格
# 第一级：总体统计 和 ROC10区间统计
# 第二级：样本数、样本占比、各阈值的正确率和均值
columns = pd.MultiIndex.from_product([
    ['总体统计', 'ROC10区间统计'],
    ['样本数', '样本占比(%)', 
     '收益率>0正确率(%)', '收益率>0均值(%)',
     '收益率>1正确率(%)', '收益率>1均值(%)',
     '收益率>3正确率(%)', '收益率>3均值(%)']
])

# 创建结果DataFrame
result_df = pd.DataFrame(index=['总体'] + roc10_labels, columns=columns)

# 填充总体统计数据
result_df.loc['总体', ('总体统计', '样本数')] = total_samples
result_df.loc['总体', ('总体统计', '样本占比(%)')] = 100.0
for threshold in thresholds:
    result_df.loc['总体', ('总体统计', f'收益率>{threshold}正确率(%)')] = yield_stats[f"收益率 > {threshold}"]["正确率(%)"]
    result_df.loc['总体', ('总体统计', f'收益率>{threshold}均值(%)')] = yield_stats[f"收益率 > {threshold}"]["均值(%)"]

# 填充ROC10区间统计数据
for interval in roc10_labels:
    if interval in roc10_yield_stats:
        result_df.loc[interval, ('ROC10区间统计', '样本数')] = roc10_yield_stats[interval]["样本数"]
        result_df.loc[interval, ('ROC10区间统计', '样本占比(%)')] = roc10_yield_stats[interval]["样本占比(%)"]
        for threshold in thresholds:
            result_df.loc[interval, ('ROC10区间统计', f'收益率>{threshold}正确率(%)')] = roc10_yield_stats[interval][f"收益率 > {threshold} 正确率(%)"]
            result_df.loc[interval, ('ROC10区间统计', f'收益率>{threshold}均值(%)')] = roc10_yield_stats[interval][f"收益率 > {threshold} 均值(%)"]

# 格式化结果表格
# 将数值格式化为两位小数
for col in result_df.columns:
    if '正确率' in col[1] or '均值' in col[1] or '样本占比' in col[1]:
        result_df[col] = result_df[col].map(lambda x: f"{x:.2f}" if pd.notnull(x) else x)

# 显示结果表格
print("\n最终结果表格:")
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)
print(result_df)

# 保存结果到CSV和Excel文件
print(f"\n保存结果到CSV文件: {output_file_csv}")
result_df.to_csv(output_file_csv, encoding='utf-8-sig')  # 使用带BOM的UTF-8编码，以便Excel正确显示中文

print(f"保存结果到Excel文件: {output_file_excel}")
result_df.to_excel(output_file_excel, sheet_name='涨停统计结果')

print("\n统计完成！") 