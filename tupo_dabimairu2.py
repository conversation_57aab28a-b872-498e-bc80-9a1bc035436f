import akshare as ak
import datetime
import time
import os
import pandas as pd
from collections import deque
import requests
import warnings
import boto3
from botocore.config import Config

warnings.filterwarnings('ignore')

# 设置保存路径和文件
token = '44055711dfeb4124a8df9531f8dd2f59'  # 在pushplus网站中可以找到
save_path = r'd:\Andy\gupiao\Data\Minutes_dabi'
predictDate = (datetime.date.today() - datetime.timedelta(days=0)).strftime("%Y%m%d")
dir_path = save_path + '/' + predictDate + '/'
dir_path2 = r'd:/Andy/gupiao/Data/Minutes' + '/' + predictDate + '/'

# Cloudflare R2配置
R2_ENDPOINT_URL = "https://5c38d853fbc5277d87a019147a8011a4.r2.cloudflarestorage.com"
R2_ACCESS_KEY_ID = "a10a932fcd224f965723f6779d70e3ca"
R2_SECRET_ACCESS_KEY = "14e2f52b70e2e3384596bcd8d34f719b3b0ce0addc0bd39d6400c0e943b23633"
R2_BUCKET_NAME = "dabimairu"

# 初始化S3客户端（用于R2）
s3_client = boto3.client(
    's3',
    endpoint_url=R2_ENDPOINT_URL,
    aws_access_key_id=R2_ACCESS_KEY_ID,
    aws_secret_access_key=R2_SECRET_ACCESS_KEY,
    config=Config(
        retries={'max_attempts': 3},
        connect_timeout=5,
        read_timeout=30
    )
)

def upload_to_r2(file_path, bucket_name=R2_BUCKET_NAME):
    """上传文件到Cloudflare R2存储桶"""
    try:
        # 获取原始文件名
        original_filename = os.path.basename(file_path)
        # 生成带时间戳的文件名（保留原始文件名，在后面添加时间戳）
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        # 分离文件名和扩展名
        filename_without_ext, file_extension = os.path.splitext(original_filename)
        file_name = f"{filename_without_ext}_{timestamp}{file_extension}"

        # print(f"开始上传文件: {file_name}")

        # 上传文件到R2
        with open(file_path, 'rb') as file:
            s3_client.upload_fileobj(
                file,
                bucket_name,
                file_name,
                ExtraArgs={
                    'ContentType': 'text/csv',
                    'ACL': 'private'
                }
            )

        # print(f"文件上传成功: {file_name}")
        # print(f"文件大小: {os.path.getsize(file_path)/1024:.2f} KB")
        return True
    except Exception as e:
        print(f"上传文件时出错: {str(e)}")
        return False

# 创建文件夹
if not os.path.exists(dir_path):
    os.makedirs(dir_path)
    os.makedirs(save_path + '/' + predictDate + '/zhangting/')

def count_csv_files(path):
    """统计指定目录下的CSV文件数量"""
    return sum(1 for filename in os.listdir(path) if filename.lower().endswith('.csv'))

file_index = count_csv_files(dir_path) + 1

def send_message(title, message):
    """发送消息到pushplus和ntfy.sh"""
    try:
        requests.post("https://ntfy.sh/Xiaoertan", data=message.encode(encoding='utf-8'))
        url = f'http://www.pushplus.plus/send?token={token}&title={title}&content={message}'
        requests.get(url)
    except Exception as e:
        print("发送消息时发生错误:", e)

sent_secIDs = {}
k = 120
while k > 0:
    ####大笔买入  symbol="大笔买入"; choice of {'火箭发射', '快速反弹', '大笔买入', '封涨停板', '打开跌停板', '有大买盘', '竞价上涨', '高开5日线', '向上缺口', '60日新高', '60日大幅上涨', '加速下跌', '高台跳水', '大笔卖出', '封跌停板', '打开涨停板', '有大卖盘', '竞价下跌', '低开5日线', '向下缺口', '60日新低', '60日大幅下跌'}
    stock_changes_em_df = ak.stock_changes_em(symbol="大笔买入")
    stock_changes_em_df['secID'] = stock_changes_em_df['代码'].astype(str).apply(lambda x: (x.zfill(6) + '.XSHE' if x.startswith(('00', '30')) or len(x) < 6 else (x.zfill(6) + '.XSHG' if x.startswith(('60', '68')) else x.zfill(6))))
    stock_changes_em_split = stock_changes_em_df['相关信息'].str.split(',', expand=True)
    # 检查实际列数并相应设置列名
    if stock_changes_em_split.shape[1] == 4:
        stock_changes_em_split.columns = ['Num', 'Price', 'chgPct', 'VolValue']
    else:
        stock_changes_em_split.columns = ['Num', 'Price', 'chgPct']
    stock_changes_em_df = pd.concat([stock_changes_em_df, stock_changes_em_split], axis=1)
    stock_changes_em_df['Num'] = pd.to_numeric(stock_changes_em_df['Num'], errors='coerce')
    stock_changes_em_df['Price'] = pd.to_numeric(stock_changes_em_df['Price'], errors='coerce')
    stock_changes_em_df['chgPct'] = pd.to_numeric(stock_changes_em_df['chgPct'], errors='coerce')
    stock_changes_em_df['chgPct'] = round(stock_changes_em_df['chgPct']*100, 2)
    stock_changes_em_df['buyin'] = round(stock_changes_em_df['Num'] * stock_changes_em_df['Price']/10000, 2)
    stock_changes_em_df.to_csv(f'{dir_path}/{predictDate}_{file_index:03d}.csv', index=False, encoding='utf-8-sig')
    stock_changes_em_df = stock_changes_em_df[(~stock_changes_em_df['名称'].str.contains(r'\*ST')) & (~stock_changes_em_df['名称'].str.contains('ST'))]
    print(f"大笔已保存到: {dir_path}")
    stock_changes_em_df_keep = stock_changes_em_df
    ### 大笔卖出
    stock_changes_em_df_maichu = ak.stock_changes_em(symbol="大笔卖出")
    stock_changes_em_split_maichu = stock_changes_em_df_maichu['相关信息'].str.split(',', expand=True)
    # 检查实际列数并相应设置列名
    if stock_changes_em_split_maichu.shape[1] == 4:
        stock_changes_em_split_maichu.columns = ['Num_Sell', 'Price_Sell', 'chgPct_Sell', 'Extra_Sell']
    else:
        stock_changes_em_split_maichu.columns = ['Num_Sell', 'Price_Sell', 'chgPct_Sell']
    stock_changes_em_df_maichu = pd.concat([stock_changes_em_df_maichu, stock_changes_em_split_maichu], axis=1)
    stock_changes_em_df_maichu['Num_Sell'] = pd.to_numeric(stock_changes_em_df_maichu['Num_Sell'], errors='coerce')
    stock_changes_em_df_maichu['Price_Sell'] = pd.to_numeric(stock_changes_em_df_maichu['Price_Sell'], errors='coerce')
    stock_changes_em_df_maichu['chgPct_Sell'] = pd.to_numeric(stock_changes_em_df_maichu['chgPct_Sell'], errors='coerce')
    stock_changes_em_df_maichu['chgPct_Sell'] = round(stock_changes_em_df_maichu['chgPct_Sell']*100, 2)
    stock_changes_em_df_maichu['Sellout'] = round(stock_changes_em_df_maichu['Num_Sell'] * stock_changes_em_df_maichu['Price_Sell']/10000, 2)
    stock_changes_em_df_maichu.drop(columns=['代码', '相关信息'], axis=1, inplace=True)
    ###  sum
    grouped_sum_maichu = stock_changes_em_df_maichu.groupby('名称')['Sellout'].sum()
    grouped_sum_df_maichu = grouped_sum_maichu.reset_index()
    stock_changes_em_df_maichu = stock_changes_em_df_maichu.merge(grouped_sum_df_maichu, on='名称', how='left')
    stock_changes_em_df_maichu = stock_changes_em_df_maichu.drop_duplicates(subset='名称', keep='first')
    stock_changes_em_df_maichu = stock_changes_em_df_maichu.sort_values(by='Sellout_y', ascending=False)
    stock_changes_em_df_maichu['Sellout_y'] = round(stock_changes_em_df_maichu['Sellout_y'], 2)
    stock_changes_em_df_maichu['Sellout_x'] = round(stock_changes_em_df_maichu['Sellout_x'], 2)
    stock_changes_em_df_maichu = stock_changes_em_df_maichu.rename(columns={'Sellout_y': '总卖出金额'})
    stock_changes_em_df_maichu = stock_changes_em_df_maichu.rename(columns={'Sellout_x': '现卖出金额'})
    ###  sum
    grouped_sum = stock_changes_em_df.groupby('名称')['buyin'].sum()
    # grouped_sum现在是一个Series，其索引是'名称'，值是对应的'buyin'求和结果
    # 如果你想要将这个求和结果作为一个新列添加到原始DataFrame中，可以使用reset_index
    grouped_sum_df = grouped_sum.reset_index()
    # 然后将这个结果合并回原始的DataFrame，只对有匹配'名称'的行进行更新
    stock_changes_em_df = stock_changes_em_df.merge(grouped_sum_df, on='名称', how='left')
    stock_changes_em_df['数量'] = stock_changes_em_df.groupby('名称').transform('size')
    stock_changes_em_df = stock_changes_em_df.drop_duplicates(subset='名称', keep='first')
    stock_changes_em_df = stock_changes_em_df.sort_values(by='buyin_y', ascending=False)
    stock_changes_em_df['buyin_y'] = round(stock_changes_em_df['buyin_y'], 2)
    stock_changes_em_df['buyin_x'] = round(stock_changes_em_df['buyin_x'], 2)
    stock_changes_em_df = stock_changes_em_df.rename(columns={'buyin_y': '总买入金额'})
    stock_changes_em_df = stock_changes_em_df.rename(columns={'buyin_x': '现买入金额'})
    # 合并版块信息
    stock_board_concept_name_ths_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_ths_merged.csv', encoding='utf-8-sig')
    stock_board_concept_name_ths_merged.drop(['secShortName'], axis=1, inplace=True)
    stock_board_concept_name_em_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_em_merged.csv', encoding='utf-8-sig')
    stock_board_concept_name_em_merged['secID'] = stock_board_concept_name_em_merged['代码'].astype(str).str.zfill(6).apply(lambda x: x + '.XSHE' if x.startswith(('00', '30')) else (x + '.XSHG' if x.startswith(('60', '68')) else x))
    stock_board_concept_name_em_merged.drop(['序号','代码','secShortName_x','最新价','涨跌幅', '涨跌额', '成交量', '成交额', '振幅', '最高', '最低', '今开', '昨收', '换手率', '市盈率-动态', '市净率'], axis=1, inplace=True)
    stock_changes_em_df=pd.merge(stock_changes_em_df, stock_board_concept_name_ths_merged, how='left', on=['secID'])
    stock_changes_em_df=pd.merge(stock_changes_em_df, stock_board_concept_name_em_merged, how='left', on=['secID'])


    # stock_board_concept_name_ths_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_ths_merged.csv', encoding='utf-8-sig')
    # stock_board_concept_name_ths_merged.drop(['序号','代码','现价','涨跌幅','振幅','成交额','涨跌','涨速', '换手', '量比', '流通股', '流通市值', '市盈率'], axis=1, inplace=True)
    # stock_board_concept_name_ths_merged = stock_board_concept_name_ths_merged.rename(columns={'secShortName_x': '名称'})
    # stock_board_concept_name_em_merged = pd.read_csv('d:/Andy/gupiao/stock_board_concept_name_em_merged.csv', encoding='utf-8-sig')
    # stock_board_concept_name_em_merged.drop(['序号','代码','最新价','涨跌幅', '涨跌额', '成交量', '成交额', '振幅', '最高', '最低', '今开', '昨收', '换手率', '市盈率-动态', '市净率'], axis=1, inplace=True)
    # stock_board_concept_name_em_merged = stock_board_concept_name_em_merged.rename(columns={'secShortName_x': '名称'})
    # stock_changes_em_df=pd.merge(stock_changes_em_df, stock_board_concept_name_ths_merged, how='left', on=['名称'])
    # stock_changes_em_df=pd.merge(stock_changes_em_df, stock_board_concept_name_em_merged, how='left', on=['名称'])

    ###
    stock_changes_em_df['总买入金额'].fillna(0, inplace=True)
    stock_changes_em_df_maichu['总卖出金额'].fillna(1, inplace=True)
    stock_changes_em_df = stock_changes_em_df.merge(stock_changes_em_df_maichu, on='名称', how='left')
    stock_changes_em_df['买卖比'] = round(stock_changes_em_df['总买入金额'] / stock_changes_em_df['总卖出金额'], 2)
    stock_changes_em_df = stock_changes_em_df.sort_values(by='买卖比', ascending=False)
    todaydate = (datetime.date.today() - datetime.timedelta(days=1)).strftime("%Y%m%d")
    path = os.path.exists(r'd:\Andy\Data\MktEqudGet\\'+todaydate+'.csv')
    while path != True:
        todaydate=(datetime.datetime.strptime(todaydate, '%Y%m%d')-datetime.timedelta(days = 1)).strftime('%Y%m%d')
        path = os.path.exists(r'd:\Andy\Data\MktEqudGet\\'+todaydate+'.csv')
    shizhi = pd.read_csv(r'd:\Andy\Data\MktEqudGet\\'+todaydate+'.csv', encoding='gbk')
    shizhi = shizhi[['secShortName','negMarketValue']]
    shizhi = shizhi.rename(columns={'secShortName': '名称'})
    stock_changes_em_df = stock_changes_em_df.merge(shizhi, on='名称', how='left')
    stock_changes_em_df['总买入占比'] = round(stock_changes_em_df['总买入金额']*10000 / stock_changes_em_df['negMarketValue'], 2)
    ## 加入融资融券信息
    rongzimingxi = pd.read_csv(r'd:\Andy\Data\融资融券明细\融资融券明细'+todaydate+'.csv')
    rongzimingxi = rongzimingxi.rename(columns={'secShortName': '名称'})
    rongzimingxi['融资买入额'] = round(rongzimingxi['融资买入额']/10000, 2)
    rongzimingxi['融资余额'] = round(rongzimingxi['融资余额']/10000, 2)
    stock_changes_em_df = stock_changes_em_df.merge(rongzimingxi, on='名称', how='left')
    ###

    ### 大笔买入
    N_shape_pool = pd.read_csv(r'd:\Andy\gupiao\N形待选_today.csv')
    stock_changes_em_df_dabi = stock_changes_em_df_keep.merge(N_shape_pool, on='secID', how='left')
    stock_changes_em_df_dabi.dropna(subset=['secShortName'], inplace=True)
    daemairu = stock_changes_em_df_dabi[(stock_changes_em_df_dabi['buyin'] > 600)]
    daemairu = daemairu[(daemairu['chgPct'] > 1) & (daemairu['chgPct'] < 8)]
    # 只留下列"收盘价格位置"的开头是5的数据
    # daemairu = daemairu[daemairu['收盘价格位置'].str.startswith('5日')]
    daemairu = daemairu.drop_duplicates(subset='secID', keep='last')
    # stock_changes_em_df_sel = stock_changes_em_df[['secID', '现买入金额', '总买入金额', '数量', '现卖出金额', '总卖出金额', '买卖比', '总买入占比']]
    # daemairu = daemairu.merge(stock_changes_em_df_sel, on='secID', how='left')
    # daemairu = daemairu[(daemairu['数量'] > 1)]
    # daemairu = daemairu.sort_values(by='buyin', ascending=True)
    # dfo = ak.stock_zh_a_spot_em()
    # dfo['secID'] = dfo['代码'].astype(str).apply(lambda x: x + '.XSHE' if x.startswith(('00', '30')) else (x + '.XSHG' if x.startswith(('60', '68')) else x))
    # dfo = dfo[['secID','涨跌幅','换手率']]
    # daemairu = daemairu.merge(dfo, on='secID', how='left')
    ###

    ### 在stock_changes_em_df中新加一列"today_sel"，当secID在daemairu中出现时为1，否则为0
    stock_changes_em_df['today_sel'] = stock_changes_em_df['secID'].apply(lambda x: 1 if x in daemairu['secID'].values else 0)
    csv_file_path = r'd:\Andy\gupiao\大笔买入'+predictDate+'_sum.csv'
    stock_changes_em_df.to_csv(csv_file_path, index=False, encoding='utf-8-sig')

    # 将这个csv文件上传到cloudflare的R2 存储桶
    upload_result = upload_to_r2(csv_file_path)
    if upload_result:
        print(f"成功将文件 {csv_file_path} 上传到Cloudflare R2存储桶")
    else:
        print(f"上传文件 {csv_file_path} 到Cloudflare R2存储桶失败")

    stock_changes_em_df_sel = stock_changes_em_df[['secID', '现买入金额', '总买入金额', '数量', '现卖出金额', '总卖出金额', '买卖比', '总买入占比']]
    daemairu = daemairu.merge(stock_changes_em_df_sel, on='secID', how='left')
    # 整理列的顺序并删减部分列
    daemairu = daemairu[['时间', 'secID', 'secShortName', '板块', 'Num', 'Price', 'chgPct', 'VolValue', 'buyin', '现买入金额', '总买入金额', '数量', '现卖出金额', '总卖出金额', '买卖比', '总买入占比', '题材', '题材汇总', '涨停原因', '题材（G）', 'tradeDate', 'ths概念名称', '板块名称', 'em板块名称']]
    # 按列'时间'倒序排列
    daemairu = daemairu.sort_values(by='时间', ascending=False)
    #####
    if len(daemairu)>0:
        file_path3d_da = f'{dir_path2}/zhangting/founded_N型.csv'
        file_path3d_da_history = f'{dir_path2}/zhangting/founded_N型_history.csv'  # 新增历史数据文件
        
        # 如果当天的文件不存在，创建新文件
        if not os.path.exists(file_path3d_da):
            daemairu.to_csv(file_path3d_da, index=False, encoding='utf-8-sig')
            # 同时保存到历史数据文件
            daemairu.to_csv(file_path3d_da_history, index=False, encoding='utf-8-sig')
        else:
            # 读取当天的数据
            existing_data_da = pd.read_csv(file_path3d_da)
            
            # 读取历史数据（如果存在）
            if os.path.exists(file_path3d_da_history):
                historical_data = pd.read_csv(file_path3d_da_history)
                # 合并历史数据和当天数据
                updated_data_da = pd.concat([historical_data, daemairu], ignore_index=True)
            else:
                # 如果历史数据文件不存在，则只使用当天的数据
                updated_data_da = pd.concat([existing_data_da, daemairu], ignore_index=True)

            # 数据清理和验证
            updated_data_da['时间'] = updated_data_da['时间'].astype(str).str.strip()
            updated_data_da['secID'] = updated_data_da['secID'].astype(str).str.strip()
            
            # 去重前的记录数
            before_drop = len(updated_data_da)
            
            # 删除相同'时间'且相同'secID'的行
            updated_data_da = updated_data_da.drop_duplicates(subset=['secID', '时间'], keep='first')
            
            # 去重后的记录数
            after_drop = len(updated_data_da)
            print(f"去重前记录数: {before_drop}, 去重后记录数: {after_drop}, 删除了 {before_drop - after_drop} 条重复记录")
            
            # 保存去重后的完整数据到当天文件和历史文件
            updated_data_da.to_csv(file_path3d_da, index=False, encoding='utf-8-sig')
            updated_data_da.to_csv(file_path3d_da_history, index=False, encoding='utf-8-sig')
            
            # 统计每个股票在不同时间点出现的次数（使用历史数据）
            stock_counts = updated_data_da.groupby('secID').size()
            # 找出出现3次及以上的股票
            qualified_stocks = stock_counts[stock_counts >= 3].index
            print(f"出现3次及以上的股票数量: {len(qualified_stocks)}")
            
            # 如果有符合条件的股票，保存到新文件
            if len(qualified_stocks) > 0:
                # 筛选出这些股票的所有记录
                three_times_stocks = updated_data_da[updated_data_da['secID'].isin(qualified_stocks)]
                # 按股票代码和时间排序
                three_times_stocks = three_times_stocks.sort_values(['secID', '时间'])
                # 保存结果
                three_times_stocks.to_csv(f'{dir_path2}/zhangting/founded_N型_3次.csv', index=False, encoding='utf-8-sig')
                print(f"发现 {len(qualified_stocks)} 只股票出现3次及以上：")
                # 打印这些股票的基本信息
                summary = three_times_stocks.groupby('secID').agg({
                    '名称': 'first',
                    '时间': 'count'
                }).rename(columns={'时间': '出现次数'})
                print(summary)

                if len(three_times_stocks)>0:
                    # daemairu.to_csv(f'{dir_path2}/zhangting/大笔买入_{file_index:03d}.csv', index=False, encoding='utf-8-sig')
                    current_time = datetime.datetime.now().time()
                    if current_time < datetime.time(14, 50) and current_time > datetime.time(9, 32):
                        combined_string = '题材_'+'时间_'+'买入额_'+'涨跌幅_'+'价格\n'+'\n'.join(
                                three_times_stocks['secID'].astype(str) + "_" +
                                three_times_stocks['secShortName'] + "_" +
                                three_times_stocks['题材'].astype(str) + "_" +
                                three_times_stocks['时间'].astype(str) + "_" +
                                three_times_stocks['buyin'].astype(str) + "_" +
                                three_times_stocks['chgPct'].astype(str) + "_" +
                                three_times_stocks['Price'].astype(str)
                            )
                        send_message("N型三点火", combined_string)
                    #####

            # 将N型股票数据上传到Cloudflare R2存储桶
            n_type_upload_result = upload_to_r2(file_path3d_da)
            if n_type_upload_result:
                print(f"成功将N型股票数据 {file_path3d_da} 上传到Cloudflare R2存储桶")
            else:
                print(f"上传N型股票数据 {file_path3d_da} 到Cloudflare R2存储桶失败")

    time.sleep(59)
    k -= 1
    file_index += 1
    current_time = datetime.datetime.now().time()
    # print(current_time)
    if current_time > datetime.time(15, 0):
        founded_sum = r'd:\Andy\gupiao\大笔买入'+predictDate+'_sum.csv'
        existing_sum = pd.read_csv(founded_sum)
        founded_N_type = f'{dir_path2}/zhangting/founded_N型.csv'
        existing_data_da = pd.read_csv(founded_N_type)
        existing_sum2 = existing_sum[existing_sum['名称'].isin(existing_data_da['名称'])]
        if len(existing_sum2) > 0:
            # print(len(existing_sum2))
            combined_string = '总买入金额_'+'数量_'+'买卖比_'+'总买入占比_'+'融资买入额\n'+'\n'.join(
                existing_sum2['secID'].astype(str) + "_" +
                existing_sum2['名称'] + "_" +
                existing_sum2['总买入金额'].astype(str) + "_" +
                existing_sum2['数量'].astype(str) + "_" +
                existing_sum2['买卖比'].astype(str) + "_" +
                existing_sum2['总买入占比'].astype(str) + "_" +
                existing_sum2['融资买入额'].astype(str)
            )
            print(combined_string)
            send_message("当日点火单汇总", combined_string)
    if current_time > datetime.time(15, 0) or (current_time > datetime.time(11, 30) and current_time < datetime.time(13, 0)):
        break
