#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
大笔买入数据导入程序
将大笔买入20250704_sum.csv文件导入到MySQL数据库的large_buy_orders表中
"""

import os
import sys
import logging
import pandas as pd
import pymysql
from datetime import datetime
from sqlalchemy import create_engine, text, MetaData, Table, Column, String, Integer, Numeric, Date, DateTime, Text, Boolean
from sqlalchemy.dialects.mysql import LONGTEXT
import warnings

# 忽略pandas警告
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('large_buy_import.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': 'stock-31bk-stock-31bankuai.c.aivencloud.com',
    'port': 26991,
    'user': 'avnadmin',
    'password': 'AVNS_daq_tCJ6LP2VwbS_633',
    'database': 'defaultdb',
    'charset': 'utf8mb4'
}

# CSV文件路径
CSV_FILE_PATH = r'c:\zhou\coding\stock_analysis\大笔买入20250704_sum.csv'
TABLE_NAME = 'large_buy_orders'

def create_database_engine():
    """创建数据库引擎"""
    try:
        connection_url = (
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}"
            f"@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['database']}"
            f"?charset={MYSQL_CONFIG['charset']}&ssl_verify_cert=false&ssl_verify_identity=false"
        )
        
        engine = create_engine(
            connection_url,
            pool_size=5,
            max_overflow=10,
            pool_timeout=10,
            pool_recycle=3600,
            pool_pre_ping=True,
            echo=False,
            connect_args={
                'connect_timeout': 10,
                'read_timeout': 60,
                'write_timeout': 60,
                'charset': 'utf8mb4'
            }
        )
        
        # 测试连接
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.fetchone():
                logger.info("✅ 数据库连接成功")
                return engine
            else:
                raise Exception("数据库连接测试失败")
                
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        raise

def load_csv_data():
    """加载CSV数据"""
    try:
        if not os.path.exists(CSV_FILE_PATH):
            raise FileNotFoundError(f"CSV文件不存在: {CSV_FILE_PATH}")
        
        logger.info(f"📁 正在读取CSV文件: {CSV_FILE_PATH}")
        
        # 读取CSV文件
        df = pd.read_csv(CSV_FILE_PATH, encoding='utf-8')
        logger.info(f"📊 CSV文件读取成功，共 {len(df)} 行数据")
        logger.info(f"📋 列名: {list(df.columns)}")
        
        return df
        
    except Exception as e:
        logger.error(f"❌ 读取CSV文件失败: {e}")
        raise

def create_table_structure(engine, df):
    """根据CSV数据创建表结构"""
    try:
        logger.info(f"🔧 正在创建表结构: {TABLE_NAME}")
        
        # 分析数据类型并创建表结构
        columns = []
        
        # 添加主键ID
        columns.append(Column('id', Integer, primary_key=True, autoincrement=True, comment='主键ID'))
        
        # 根据CSV列创建表字段
        for col_name in df.columns:
            # 清理列名，移除特殊字符
            clean_col_name = col_name.replace('(', '_').replace(')', '_').replace('（', '_').replace('）', '_')
            clean_col_name = clean_col_name.replace(' ', '_').replace('-', '_').replace('.', '_')
            
            # 根据数据内容判断字段类型
            sample_data = df[col_name].dropna()
            if len(sample_data) > 0:
                sample_value = str(sample_data.iloc[0])
                
                # 判断数据类型
                if col_name in ['时间_x', '时间_y']:
                    columns.append(Column(clean_col_name, String(20), comment=f'{col_name}'))
                elif col_name in ['代码', 'secID', '证券代码']:
                    columns.append(Column(clean_col_name, String(20), comment=f'{col_name}'))
                elif col_name in ['Num', 'Num_Sell', '数量']:
                    columns.append(Column(clean_col_name, Integer, comment=f'{col_name}'))
                elif col_name in ['Price', 'Price_Sell', 'chgPct', 'chgPct_Sell', 'VolValue', 
                                '现买入金额', '总买入金额', '现卖出金额', '总卖出金额', '买卖比', 
                                'negMarketValue', '总买入占比', '融资买入额', '融资余额', '买入额余额比例']:
                    columns.append(Column(clean_col_name, Numeric(15, 4), comment=f'{col_name}'))
                elif col_name in ['today_sel']:
                    columns.append(Column(clean_col_name, Integer, comment=f'{col_name}'))
                elif len(sample_value) > 500:
                    columns.append(Column(clean_col_name, LONGTEXT, comment=f'{col_name}'))
                elif len(sample_value) > 100:
                    columns.append(Column(clean_col_name, Text, comment=f'{col_name}'))
                else:
                    columns.append(Column(clean_col_name, String(255), comment=f'{col_name}'))
            else:
                columns.append(Column(clean_col_name, String(255), comment=f'{col_name}'))
        
        # 添加时间戳字段
        columns.append(Column('created_at', DateTime, default=datetime.now, comment='创建时间'))
        columns.append(Column('updated_at', DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间'))
        
        # 创建表
        metadata = MetaData()
        table = Table(TABLE_NAME, metadata, *columns)
        
        # 删除现有表（如果存在）
        with engine.connect() as conn:
            conn.execute(text(f"DROP TABLE IF EXISTS {TABLE_NAME}"))
            conn.commit()
            logger.info(f"🗑️ 已删除现有表: {TABLE_NAME}")
        
        # 创建新表
        metadata.create_all(engine)
        logger.info(f"✅ 表结构创建成功: {TABLE_NAME}")
        
        return table
        
    except Exception as e:
        logger.error(f"❌ 创建表结构失败: {e}")
        raise

def import_data_to_mysql(engine, df):
    """将数据导入到MySQL"""
    try:
        logger.info(f"📤 开始导入数据到表: {TABLE_NAME}")
        
        # 清理列名
        df_clean = df.copy()
        df_clean.columns = [
            col.replace('(', '_').replace(')', '_').replace('（', '_').replace('）', '_')
               .replace(' ', '_').replace('-', '_').replace('.', '_')
            for col in df_clean.columns
        ]
        
        # 添加时间戳
        df_clean['created_at'] = datetime.now()
        df_clean['updated_at'] = datetime.now()
        
        # 批量导入数据 - 使用更小的批次大小避免参数过多错误
        batch_size = 50  # 减小批次大小
        total_rows = len(df_clean)
        imported_rows = 0

        for i in range(0, total_rows, batch_size):
            batch_df = df_clean.iloc[i:i+batch_size]
            try:
                batch_df.to_sql(
                    TABLE_NAME,
                    engine,
                    if_exists='append',
                    index=False,
                    method='multi'
                )
                imported_rows += len(batch_df)
                logger.info(f"📊 已导入 {imported_rows}/{total_rows} 行数据 ({imported_rows/total_rows*100:.1f}%)")
            except Exception as e:
                logger.warning(f"⚠️ 批次导入失败，尝试逐行导入: {e}")
                # 如果批次导入失败，尝试逐行导入
                for _, row in batch_df.iterrows():
                    try:
                        row_df = pd.DataFrame([row])
                        row_df.to_sql(
                            TABLE_NAME,
                            engine,
                            if_exists='append',
                            index=False,
                            method='multi'
                        )
                        imported_rows += 1
                    except Exception as row_e:
                        logger.error(f"❌ 行导入失败: {row_e}")
                        continue
                logger.info(f"📊 已导入 {imported_rows}/{total_rows} 行数据 ({imported_rows/total_rows*100:.1f}%)")
        
        logger.info(f"✅ 数据导入完成！共导入 {imported_rows} 行数据")
        
        # 验证导入结果
        with engine.connect() as conn:
            result = conn.execute(text(f"SELECT COUNT(*) as count FROM {TABLE_NAME}"))
            count = result.fetchone()[0]
            logger.info(f"🔍 数据库中实际记录数: {count}")
        
        return imported_rows
        
    except Exception as e:
        logger.error(f"❌ 数据导入失败: {e}")
        raise

def main():
    """主函数"""
    try:
        logger.info("🚀 开始大笔买入数据导入程序")
        logger.info(f"📁 CSV文件路径: {CSV_FILE_PATH}")
        logger.info(f"🗄️ 目标表名: {TABLE_NAME}")
        
        # 1. 创建数据库连接
        engine = create_database_engine()
        
        # 2. 加载CSV数据
        df = load_csv_data()
        
        # 3. 创建表结构
        create_table_structure(engine, df)
        
        # 4. 导入数据
        imported_rows = import_data_to_mysql(engine, df)
        
        logger.info("🎉 大笔买入数据导入程序执行完成！")
        logger.info(f"📊 导入统计: {imported_rows} 行数据成功导入到 {TABLE_NAME} 表")
        
        return True
        
    except Exception as e:
        logger.error(f"💥 程序执行失败: {e}")
        return False
    finally:
        if 'engine' in locals():
            engine.dispose()
            logger.info("🔌 数据库连接已关闭")

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 大笔买入数据导入成功！")
    else:
        print("\n❌ 大笔买入数据导入失败！")
        sys.exit(1)
