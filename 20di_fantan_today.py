import os
import pandas as pd
from datetime import datetime, timedelta
import requests
import os
import numpy as np
from 股票技术指标评分系统_today import score_stock_data, batch_score_files

def load_data_efficiently(date):
    """只读取必要的列，减少内存使用"""
    needed_columns = {
        'equd': ['secID', 'secShortName', 'ticker', 'exchangeCD', 'chgPct', 'closePrice', 
                 'lowestPrice', 'highestPrice', 'preClosePrice', 'actPreClosePrice', 'turnoverVol', 'turnoverRate', 'openPrice'],
        'limit': ['secID', 'secShortName', 'limitUpPrice', 'limitDownPrice'],
        'factors': ['secID', 'MA5', 'MA10', 'MA20', 'MA60', 'EMA5', 'EMA10', 'EMA20', 'EMA60', 'RSI', 'KDJ_K', 'KDJ_D', 'KDJ_J', 'OBV', 'OBV6', 'OBV20', 'MACD', 'VEMA5', 'VEMA10', 'VEMA12', 'VEMA26', 'BollUp', 'BollDown', 'ATR6', 'ATR14', 'PSY', 'AR', 'BR', 'VR', 'BIAS5', 'BIAS10', 'BIAS20', 'BIAS60', 'ROC6', 'ROC20', 'EMV6', 'EMV14', 'MTM', 'MTMMA', 'PVT', 'PVT6', 'PVT12', 'TRIX5', 'TRIX10', 'MFI', 'WVAD', 'ChaikinOscillator', 'ChaikinVolatility', 'ASI', 'ARBR', 'CR20', 'ADTM', 'DDI', 'DEA', 'DIFF', 'BBI', 'TRIX5', 'TRIX10', 'UOS', 'MA10RegressCoeff12', 'MA10RegressCoeff6']
    }

    
    try:
        equd_df = pd.read_csv(os.path.join(equd_dir, f"{date}.csv"), 
                             encoding='gbk', usecols=needed_columns['equd'])
        limit_df = pd.read_csv(os.path.join(limit_dir, f"{date}.csv"), 
                              encoding='gbk', usecols=needed_columns['limit'])
        factors_df = pd.read_csv(os.path.join(mkt_stock_factors_dir, f"{date}.csv"), 
                                encoding='gbk', usecols=needed_columns['factors'])
        return pd.merge(pd.merge(equd_df, limit_df, on=["secID", "secShortName"]), 
                       factors_df, on=["secID"])
    except Exception as e:
        print(f"读取{date}数据出错: {e}")
        return None

def process_stock_data(group):
    """处理单个股票的数据"""
    if len(group) < 15:  # 修改为至少需要15天数据
        return []
    
    results = []
    group = group.reset_index(drop=True)
    
    print(f"\n处理股票: {group.iloc[0]['secShortName']} ({group.iloc[0]['secID']})")
    print(f"数据天数: {len(group)}")
    
    # 计算15天滚动最低价和最高价
    rolling_min = group['lowestPrice'].rolling(window=15, min_periods=15).min()
    rolling_max = group['highestPrice'].rolling(window=15, min_periods=15).max()
    
    found_patterns = 0
    for i in range(14, len(group)):  # 从第15天开始，确保有足够的历史数据
        current_min = rolling_min[i]  # 当前15天的最低价
        current_max = rolling_max[i]  # 当前15天的最高价
        
        if group.loc[i, "lowestPrice"] <= current_min: 
            # 基础条件检查
            if (i+1 < len(group) and 
                rolling_min[i] > 0 and 
                group.loc[i, "lowestPrice"] > 0 and 
                group.loc[i+1, "highestPrice"] > 0):
                
                # 第二天放量上涨检查（成交量是第一天的2倍，且不涨停，且最高价大于等于前15天最高价）
                if (group.loc[i+1, "turnoverVol"] >= 2 * group.loc[i, "turnoverVol"] and 
                    group.loc[i+1, "chgPct"] > 0 and 
                    group.loc[i+1, "closePrice"] < group.loc[i+1, "limitUpPrice"] and
                    group.loc[i+1, "highestPrice"] >= current_max):  # 新增条件：最高价大于等于前15天最高价
                    
                    result = {
                        "secID": group.iloc[0]["secID"],
                        "secShortName": group.iloc[0]["secShortName"],
                        "前15日最低价": rolling_min[i],
                        "前15日最高价": rolling_max[i],  # 新增字段
                        "i_最低价": group.loc[i, "lowestPrice"],
                        "i_涨跌幅": group.loc[i, "chgPct"],
                        "i1_反弹日期": group.loc[i+1, "date"],
                        "i1_涨跌幅": group.loc[i+1, "chgPct"],
                        "i1_成交量": group.loc[i+1, "turnoverVol"],
                        "i1_成交量比": group.loc[i+1, "turnoverVol"] / group.loc[i, "turnoverVol"],
                        "i1_最高价": group.loc[i+1, "highestPrice"],  # 新增字段
                        "i1_最高_收盘比例": group.loc[i+1, "highestPrice"]/group.loc[i+1, "closePrice"]-1,
                        "i1_closePrice": group.loc[i+1, "closePrice"]
                    }
                    
                    # 添加第二天(i+1)相比第一天(i)的factors列比值
                    factor_cols = ['MA5', 'MA10', 'MA20', 'MA60', 'EMA5', 'EMA10', 'EMA20', 'EMA60', 
                                  'RSI', 'KDJ_K', 'KDJ_D', 'KDJ_J', 'OBV', 'OBV6', 'OBV20', 'MACD', 
                                  'VEMA5', 'VEMA10', 'VEMA12', 'VEMA26', 'BollUp', 'BollDown', 'ATR6', 
                                  'ATR14', 'PSY', 'AR', 'BR', 'VR', 'BIAS5', 'BIAS10', 'BIAS20', 'BIAS60', 
                                  'ROC6', 'ROC20', 'EMV6', 'EMV14', 'MTM', 'MTMMA', 'PVT', 'PVT6', 'PVT12', 
                                  'TRIX5', 'TRIX10', 'MFI', 'WVAD', 'ChaikinOscillator', 'ChaikinVolatility', 
                                  'ASI', 'ARBR', 'CR20', 'ADTM', 'DDI', 'DEA', 'DIFF', 'BBI', 'UOS', 
                                  'MA10RegressCoeff12', 'MA10RegressCoeff6']
                    
                    for col in factor_cols:
                        if col in group.columns:
                            current_value = group.loc[i+1, col]  # 第二天的值
                            prev_value = group.loc[i, col]       # 第一天的值
                            if prev_value != 0 and not pd.isna(current_value) and not pd.isna(prev_value):  # 避免除零错误和NaN
                                ratio = current_value / prev_value
                                result[f"i1_i_{col}_ratio"] = ratio
                    
                    found_patterns += 1
                    results.append(result)
    
    print(f"找到符合条件的模式数量: {found_patterns}")
    return results

# 2. 然后是全局变量定义
token = '44055711dfeb4124a8df9531f8dd2f59'
equd_dir = "d:\\Andy\\Data\\MktEqudGet\\"
limit_dir = "d:\\Andy\\Data\\MktLimitGet\\"
mkt_stock_factors_dir = r'd:\Andy\Data\MktStockFactorsOneDayGet'

# 3. 主程序代码
end_date = (datetime.today() - timedelta(days=0)).strftime("%Y%m%d")
start_date = (datetime.strptime(end_date, '%Y%m%d') - timedelta(days=30)).strftime("%Y%m%d")
print("Start date:", start_date)
print("End date:", end_date)
output_file = 'd:\\Andy\\gupiao\\15di_fantan_twiceVol_today.csv'

# 获取日期范围内的交易日
trading_dates = sorted([f.split('.')[0] for f in os.listdir(equd_dir) 
                      if f.endswith('.csv') and start_date <= f.split('.')[0] <= end_date])

print(f"找到交易日数量: {len(trading_dates)}")

# 创建一个空的DataFrame来存储所有日期的数据
all_data = pd.DataFrame()

# 读取所有日期的数据并合并
for date in trading_dates:
    daily_data = load_data_efficiently(date)
    if daily_data is not None:
        daily_data['date'] = date  # 添加日期列
        all_data = pd.concat([all_data, daily_data], ignore_index=True)

# 应用过滤条件
filtered_data = all_data[
    (all_data['ticker'] < 700000) & 
    (all_data['exchangeCD'].isin(["XSHE", "XSHG"])) & 
    (~all_data['secShortName'].str.contains('B|ST')) & 
    (all_data['closePrice'] > 3)
]

all_results = []
# 按股票代码分组，这样每个group就会包含该股票在整个日期范围内的所有数据
for secID, group in filtered_data.groupby("secID"):
    # 按日期排序
    group = group.sort_values('date')
    results = process_stock_data(group)
    all_results.extend(results)

print("\n最终结果统计:")
print(f"找到的总模式数量: {len(all_results)}")

results_df = pd.DataFrame(all_results)
if not results_df.empty:  
    # 获取最大的反弹日期
    max_date = results_df['i1_反弹日期'].max()  
    # 只保留列“i1_反弹日期”的值最大的那些行 
    results_df = results_df[results_df['i1_反弹日期'] == max_date]
    # 保存结果
    results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    result_df = score_stock_data(output_file, output_file)
    print(f"\n结果已保存到: {output_file}")