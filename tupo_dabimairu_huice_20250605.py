from datetime import datetime, timedelta, date
import time
import os
import pandas as pd
from collections import deque, defaultdict
import requests
import warnings

warnings.filterwarnings('ignore')

save_path = r'd:\Andy\gupiao\Data\Minutes_dabi'
predictDate = (date.today() - timedelta(days=0)).strftime("%Y%m%d")
print(predictDate)
dir_path = save_path + '/' + predictDate + '/'
Nxing_date = (datetime.strptime(predictDate, '%Y%m%d') - timedelta(days=1)).strftime("%Y%m%d")

def count_csv_files(path):
    """统计指定目录下的CSV文件数量"""
    return sum(1 for filename in os.listdir(path) if filename.lower().endswith('.csv'))

def get_time_slot(time_str):
    """将时间字符串转换为分钟时间槽"""
    hour, minute, second = map(int, time_str.split(':'))
    total_minutes = hour * 60 + minute
    return total_minutes // 1

# 创建字典来记录每个股票在不同时间槽的出现情况
stock_time_slots = defaultdict(set)
# 创建字典来存储每个股票在每个时间点的数据，使用时间作为key避免重复
stock_time_data = defaultdict(dict)

file_index = 1
k = count_csv_files(dir_path)

while k > 0:
    print(file_index)
    file_path = f'{dir_path}/{predictDate}_{file_index:03d}.csv'
    
    if os.path.exists(file_path):
        stock_changes_em_df = pd.read_csv(file_path)
        # 先判断N形待选池是否存在
        while not os.path.exists(r'N形待选_' + Nxing_date + '.csv'):
            Nxing_date = (datetime.strptime(Nxing_date, '%Y%m%d') - timedelta(days=1)).strftime("%Y%m%d")
        N_shape_pool = pd.read_csv(r'N形待选_' + Nxing_date + '.csv')
        # 合并N形待选池
        stock_changes_em_df_dabi = stock_changes_em_df.merge(N_shape_pool, on='secID', how='left')
        stock_changes_em_df_dabi.dropna(subset=['secShortName'], inplace=True)
        
        # 筛选符合条件的股票
        daemairu = stock_changes_em_df_dabi[(stock_changes_em_df_dabi['buyin'] > 600)]
        daemairu = daemairu[(daemairu['chgPct'] > 1) & (daemairu['chgPct'] < 8)]
        # daemairu = daemairu[daemairu['收盘价格位置'].str.startswith('5日')]
        daemairu = daemairu[daemairu['时间'] >= '09:32:00']
        
        # 更新每只股票的出现记录和数据
        for _, row in daemairu.iterrows():
            secID = row['secID']
            time_str = row['时间']
            time_slot = get_time_slot(time_str)
            
            # 记录该股票在这个时间槽的出现
            stock_time_slots[secID].add(time_slot)
            
            # 使用时间作为key来存储数据，避免重复
            stock_time_data[secID][time_str] = row.to_dict()
    
    k -= 1
    file_index += 1

# 创建结果DataFrame
all_records = []
for secID, time_slots in stock_time_slots.items():
    if len(time_slots) >= 4:  # 只处理在不同时间槽出现3次及以上的股票
        # 获取该股票的所有时间点记录
        records = list(stock_time_data[secID].values())  # 使用values()来获取唯一的记录
        # 为每条记录添加出现次数
        for record in records:
            record['出现次数'] = len(time_slots)
            all_records.append(record)

# 转换为DataFrame并按时间排序
filtered_stocks = pd.DataFrame(all_records)
if not filtered_stocks.empty:
    # 确保按secID和时间排序，并去除完全重复的行
    filtered_stocks = filtered_stocks.sort_values(['secID', '时间']).drop_duplicates()

# 保存符合条件的股票到founded_N型.csv
if len(filtered_stocks) > 0:
    
    # 保存结果
    filtered_stocks.to_csv(f'founded_N型_' + predictDate + '.csv', index=False, encoding='utf-8-sig')
    
    # 打印统计信息
    unique_stocks = filtered_stocks['secID'].nunique()
    total_records = len(filtered_stocks)
    print(f"处理完成，共找到{unique_stocks}支符合条件的股票，总计{total_records}条记录")
