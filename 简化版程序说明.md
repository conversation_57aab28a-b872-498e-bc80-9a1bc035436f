# 简化版股票大笔买入监控程序说明

## 修改内容总结

### 已删除的功能：
1. **三次买入检测算法** - 删除了复杂的时间槽分析和多次出现检测
2. **总买入金额>30000万元预警** - 删除了高金额预警逻辑
3. **出现次数>3次预警** - 删除了基于出现频次的预警
4. **N型三点火预警** - 删除了复杂的模式识别预警
5. **时间槽函数** - 删除了`get_time_slot()`函数
6. **复杂的历史文件分析循环** - 简化了数据处理逻辑

### 保留的核心功能：

#### 1. 基本筛选条件
- **买入金额 > 800万元**
- **涨跌幅在-5%到8%之间**
- **交易时间在9:31之后**

#### 2. 价格下跌预警
- 监控符合条件股票的价格变化
- 当股票最新价格跌至最低价时触发预警
- 避免重复发送同一股票的预警

#### 3. 数据处理流程
- 每分钟获取大笔买入数据
- 应用筛选条件过滤股票
- 保存符合条件的股票到CSV文件
- 实时监控价格变化并发送预警

## 程序运行逻辑

### 主循环（每分钟执行一次，共120次）：
1. 获取大笔买入数据
2. 数据预处理（股票代码标准化、买入金额计算）
3. 应用筛选条件
4. 保存符合条件的股票
5. 价格下跌预警检测
6. 发送预警消息

### 筛选条件详解：
```python
# 基本筛选条件
daemairu = stock_changes_em_df_dabi[(stock_changes_em_df_dabi['buyin'] > 800)]
daemairu = daemairu[(daemairu['chgPct'] > -5) & (daemairu['chgPct'] < 8)]
daemairu = daemairu[daemairu['时间'] >= '09:31:00']
```

### 价格下跌预警逻辑：
```python
# 找出价格跌至最低价的股票
price_drop_stocks = monitored_stocks[monitored_stocks['最新价'] <= monitored_stocks['最低']]
```

## 输出文件

1. **founded_符合条件.csv** - 保存所有符合筛选条件的股票记录
2. **价格下跌提醒_{日期}.csv** - 保存价格下跌预警记录
3. **大笔买入{日期}_sum.csv** - 保存当日大笔买入汇总数据

## 预警消息

1. **价格下跌预警** - 当符合条件的股票价格跌至最低价时发送
2. **当日符合条件股票汇总** - 收盘后发送当日汇总信息

## 程序优势

1. **简化逻辑** - 去除复杂算法，提高程序稳定性
2. **实时监控** - 保持每分钟的实时数据更新
3. **精准预警** - 专注于价格下跌这一关键指标
4. **避免重复** - 防止同一股票重复发送预警
5. **数据完整** - 保留所有必要的股票信息和分析数据

## 使用建议

1. 程序适合在交易时间运行（9:30-15:00）
2. 关注价格下跌预警，及时调整投资策略
3. 定期查看符合条件的股票文件，分析市场趋势
4. 结合其他技术指标进行综合分析
