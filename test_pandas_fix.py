#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Pandas数据类型兼容性修复
"""

import pandas as pd
import numpy as np
import warnings

def test_pandas_dtype_fix():
    """测试修复后的Pandas数据类型处理"""
    print("=== 测试Pandas数据类型兼容性修复 ===")
    
    # 创建测试数据，模拟原始问题场景
    test_data = {
        'secID': ['000001.XSHE', '000002.XSHE', '600000.XSHG', '600001.XSHG'],
        '名称': ['平安银行', '万科A', '浦发银行', '邮储银行'],
        '题材': [np.nan, '房地产', np.nan, '大金融'],  # 包含NaN值，可能被推断为float64
        '涨停原因': ['资产重组 其他', '大消费 房地产', '电力体制改革 其他', '大金融 业绩增长']
    }
    
    df = pd.DataFrame(test_data)
    print("原始数据类型:")
    print(df.dtypes)
    print("\n原始数据:")
    print(df)
    
    # 应用修复方案
    print("\n=== 应用修复方案 ===")
    
    # 确保'题材'列为字符串类型，避免数据类型兼容性问题
    if '题材' in df.columns:
        df['题材'] = df['题材'].astype('object')
        print("已将'题材'列转换为object类型")
    
    # 如果'题材'列为空，则从'涨停原因'列提取内容
    if '涨停原因' in df.columns:
        mask = df['题材'].isna()
        if mask.any():
            print(f"发现{mask.sum()}个空值需要填充")
            extracted_values = df.loc[mask, '涨停原因'].str.split(n=1).str[0]
            print("提取的值:", extracted_values.tolist())
            df.loc[mask, '题材'] = extracted_values
            print("已成功填充空值")
    
    print("\n修复后数据类型:")
    print(df.dtypes)
    print("\n修复后数据:")
    print(df)
    
    # 测试是否还会产生警告
    print("\n=== 测试是否还有警告 ===")
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        
        # 再次执行相同操作
        mask = df['题材'].isna()
        if mask.any():
            extracted_values = df.loc[mask, '涨停原因'].str.split(n=1).str[0]
            df.loc[mask, '题材'] = extracted_values
        
        if w:
            print(f"警告数量: {len(w)}")
            for warning in w:
                print(f"警告: {warning.message}")
        else:
            print("✅ 没有产生任何警告！修复成功！")
    
    return df

if __name__ == "__main__":
    result_df = test_pandas_dtype_fix()
    print("\n=== 测试完成 ===")
