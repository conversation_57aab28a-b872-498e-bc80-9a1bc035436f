#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试带字段截断功能的数据导入
"""

import os
import sys
import logging
import pandas as pd
from datetime import datetime
from sqlalchemy import create_engine, text

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': 'stock-31bk-stock-31bankuai.c.aivencloud.com',
    'port': 26991,
    'user': 'avnadmin',
    'password': 'AVNS_daq_tCJ6LP2VwbS_633',
    'database': 'defaultdb',
    'charset': 'utf8mb4'
}

TABLE_NAME = 'n_shape_candidates'

def create_test_csv():
    """创建包含超长字段的测试CSV文件"""
    test_data = {
        'secID': ['000001.XSHE', '000002.XSHE', '600000.XSHG'],
        'secShortName': ['平安银行', '万科A', '浦发银行'],
        '涨停日期': ['********', '********', '********'],
        '缩量下跌天数': [3, 2, 4],
        '持续缩量': ['持续缩量', '否', '持续缩量'],
        '最后一天收盘价': [10.5, 8.2, 12.3],
        '最低价格位置': ['5日线上', '10日线以下', '5日线以下'],
        '收盘价格位置': ['5日线上', '5日线以下', '10日线以下'],
        # 添加一个可能超长的字段用于测试
        '题材汇总': [
            '新能源汽车 新能源汽车 锂电池 锂电池 充电桩 新能源汽车 储能 新能源汽车 锂电池 充电桩',
            '人工智能 机器学习 深度学习 神经网络 自然语言处理 计算机视觉 大数据 云计算 物联网',
            '这是一个非常长的题材描述，包含了很多重复的内容和关键词，用来测试字段截断功能是否能够正常工作，应该会被智能截断或去重处理'
        ]
    }
    
    df = pd.DataFrame(test_data)
    test_csv_path = 'd:\\Andy\\gupiao\\test_N形待选_truncation.csv'
    df.to_csv(test_csv_path, index=False, encoding='utf-8-sig')
    logger.info(f"✅ 测试CSV文件创建成功: {test_csv_path}")
    
    # 显示字段长度信息
    for col in df.columns:
        max_len = df[col].astype(str).str.len().max()
        logger.info(f"📏 字段 {col} 最大长度: {max_len} 字符")
    
    return test_csv_path

def test_import_functions():
    """测试导入函数"""
    try:
        # 导入修改后的函数
        sys.path.append('d:\\Andy\\coding\\gupiao_huice')
        
        # 创建数据库引擎
        connection_url = (
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}"
            f"@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['database']}"
            f"?charset={MYSQL_CONFIG['charset']}&ssl_verify_cert=false&ssl_verify_identity=false"
        )
        
        engine = create_engine(
            connection_url,
            pool_size=5,
            max_overflow=10,
            pool_timeout=10,
            pool_recycle=3600,
            pool_pre_ping=True,
            echo=False,
            connect_args={
                'connect_timeout': 10,
                'read_timeout': 60,
                'write_timeout': 60,
                'charset': 'utf8mb4'
            }
        )
        
        # 测试连接
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.fetchone():
                logger.info("✅ 数据库连接成功")
            else:
                raise Exception("数据库连接测试失败")
        
        # 创建测试CSV
        test_csv_path = create_test_csv()
        
        # 测试字段长度获取函数
        def get_table_field_lengths(engine):
            """获取数据库表的字段长度限制"""
            try:
                logger.info(f"🔍 正在获取表字段长度限制: {TABLE_NAME}")
                field_lengths = {}
                
                with engine.connect() as conn:
                    result = conn.execute(text(f"DESCRIBE {TABLE_NAME}"))
                    columns = result.fetchall()
                    
                    for col in columns:
                        field_name = col[0]
                        field_type = col[1]
                        
                        # 解析字段长度
                        max_length = None
                        if 'varchar' in field_type.lower():
                            # 提取varchar长度
                            start = field_type.find('(')
                            end = field_type.find(')')
                            if start != -1 and end != -1:
                                max_length = int(field_type[start+1:end])
                        elif 'text' in field_type.lower():
                            if 'longtext' in field_type.lower():
                                max_length = 4294967295  # LONGTEXT最大长度
                            elif 'mediumtext' in field_type.lower():
                                max_length = 16777215    # MEDIUMTEXT最大长度
                            else:
                                max_length = 65535       # TEXT最大长度
                        
                        if max_length:
                            field_lengths[field_name] = max_length
                    
                    logger.info(f"✅ 获取到 {len(field_lengths)} 个字段的长度限制")
                    return field_lengths
                    
            except Exception as e:
                logger.error(f"❌ 获取字段长度限制失败: {e}")
                return {}
        
        # 测试字段长度获取
        field_lengths = get_table_field_lengths(engine)
        if field_lengths:
            logger.info("✅ 字段长度获取测试通过")
            for field, length in field_lengths.items():
                if 'varchar' in str(length) or length < 1000:  # 只显示可能有限制的字段
                    logger.info(f"  📝 {field}: 最大长度 {length}")
        else:
            logger.warning("⚠️ 未获取到字段长度信息")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False
    finally:
        if 'engine' in locals():
            engine.dispose()

if __name__ == "__main__":
    logger.info("🚀 开始测试带字段截断功能的数据导入")
    
    if test_import_functions():
        print("\n✅ 字段截断功能测试通过！")
    else:
        print("\n❌ 字段截断功能测试失败！")
